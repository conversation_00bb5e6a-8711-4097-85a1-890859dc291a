<script lang="ts" setup>
withDefaults(defineProps<{
  title?: string;
}>(), {
  title: '',
});
</script>


<template>
  <div class="pima-wrapper-form-title">
    <div class="wrapper-form-title-header">
      {{ title }}
    </div>
    <div class="wrapper-form-title-body">
      <slot />
    </div>
  </div>
</template>


<style lang="less" scoped>
.pima-wrapper-form-title {
  margin:30px auto;
}
</style>
