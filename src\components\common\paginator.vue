<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import _ from 'lodash';
import { useI18n } from 'vue-i18n';


interface Props {
  total?: number;
  pageIndex?: number;
  pageSize?: number;
  pageSizeOptions?: number[];
  simple?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  total: 0,
  pageIndex: 1,
  pageSize: 15,
  pageSizeOptions() {
    return [15, 30, 50];
  },
  simple: false,
});


const emit = defineEmits([
  'update:pageIndex',
  'update:pageSize',
  'change',
  'page-index-change',
  'page-size-change',
]);


const { t } = useI18n();
const jumpPage = ref(props.pageIndex);
const minPage = 1;


const pageCount = computed(() => {
  const c = Math.ceil(props.total / props.pageSize);
  return c;
});


watch(
  () => props.pageIndex,
  (newVal) => {
    jumpPage.value = newVal;
  },
);


// 当 pageIndex 不为 1 pageSize 发生变化时
// iview 的 page 组件会连续发送 page size change 和 page index change
// 通过 debounce 将这两个事件"合并"成一个事件
function triggerEmitChange() {
  emit('change');
}

const debounceTriggerEmitChange = _.debounce(triggerEmitChange, 10);

function emitChange() {
  debounceTriggerEmitChange();
}

function onPageIndexChange(index) {
  if (index > pageCount.value) {
    emit('update:pageIndex', pageCount.value);
    emit('page-index-change', pageCount.value);
    emitChange();
  } else if (index < 1) {
    emit('update:pageIndex', 1);
    emit('page-index-change', 1);
    emitChange();
  } else {
    emit('update:pageIndex', index);
    emit('page-index-change', index);
    emitChange();
  }
}

function onPageSizeChange(size) {
  emit('update:pageSize', size);
  emit('page-size-change', size);
  emitChange();
}

function onBlur() {
  if ([undefined, null].includes(jumpPage.value)) {
    jumpPage.value = minPage;
  }
}
</script>


<template>
  <div class="pima-paginator-wrapper">
    <div class="paginator">
      <template v-if="!simple">
        <span class="total">
          <!-- {{ t('paginator.pageInfo', { pageCount, itemCount: total }) }} -->
          {{ t('paginator.pageInfo[0]') }}
          <span class="count">{{ total }}</span>
          {{ t('paginator.pageInfo[1]') }}
        </span>
      </template>

      <Page
        :show-sizer="true"
        :total="total"
        :model-value="pageIndex"
        :page-size="pageSize"
        :page-size-opts="pageSizeOptions"
        @on-change="onPageIndexChange"
        @on-page-size-change="onPageSizeChange"
      />

      <template v-if="!simple">
        <span class="jump-to">{{ t('paginator.jumpTo') }}</span>

        <InputNumber
          v-model="jumpPage"
          class="paget-index-input"
          :precision="0"
          :min="minPage"
          :max="pageCount"
          :value="pageIndex"
          @on-blur="onBlur"
        />

        <span class="page">{{ t('paginator.page', { pageCount }) }}</span>

        <Button
          class="jump-button"
          type="primary"
          @click="onPageIndexChange(jumpPage)"
        >
          {{ t('paginator.jump') }}
        </Button>
      </template>
    </div>
  </div>
</template>
