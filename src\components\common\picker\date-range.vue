<script setup lang="ts">
import { computed } from 'vue';
import { isValid } from 'date-fns';
import PickerDate from './date.vue';

import { useDateRange } from '@/uses/date-range';
import { namespaceT } from '@/helps/namespace-t';

type ModelDate = Date | string | null;

defineOptions({
  name: 'PickerDateRange',
});

const emit = defineEmits<{
  'on-change': [];
}>();

const modelMax = defineModel<ModelDate>('max');
const modelMin = defineModel<ModelDate>('min');

enum ValidTypes {
  Date = 'date',
  Month = 'month',
  Year = 'year',
}

interface Props {
  disabled?: boolean;
  disableEnd?: boolean,
  type?: ValidTypes;
  format?: string;
  // min: ModelDate;
  // max: ModelDate;
  limitMin?: Date | null;
  limitMax?: Date | null;
  transfer?: boolean;
  minPlaceholder?: string | undefined;
  maxPlaceholder?: string | undefined;
}


const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  disableEnd: false,
  type: ValidTypes.Date,
  format: 'yyyy-MM-dd',
  // min: null,
  // max: null,
  limitMin: null,
  limitMax: null,
  transfer: false,
  minPlaceholder: undefined,
  maxPlaceholder: undefined,
});

const t = namespaceT('common');

const disableEndDateComp = computed<boolean>(() => {
  return props.disabled || props.disableEnd;
});

function getDateByType(date) {
  if (!isValid(date) || props.type === ValidTypes.Date) {
    return date;
  }

  const y = date.getFullYear();
  const m = date.getMonth();
  return new Date(y, props.type === ValidTypes.Month ? m : 0, 1);
}

const dateRange = useDateRange({
  min: () => getDateByType(modelMin.value),
  max: () => getDateByType(modelMax.value),
}, {
  min: () => getDateByType(props.limitMin),
  max: () => getDateByType(props.limitMax),
});

const onEndDateChange = (val: ModelDate) => {
  if (val) {
    modelMax.value = new Date(val);
  } else {
    modelMax.value = null;
  }
  emit('on-change');
};

const onStartDateChange = (val: ModelDate) => {
  if (val) {
    modelMin.value = new Date(val);
  } else {
    modelMin.value = null;
  }
  emit('on-change');
};
</script>


<template>
  <div class="date-range-picker">
    <PickerDate
      v-model="modelMin"
      :disabled="disabled"
      :type="type"
      :format="format"
      :transfer="transfer"
      :disabled-date="dateRange.minDisabled"
      :placeholder="minPlaceholder || t('placeholder.startDate')"
      @on-change="onStartDateChange"
    />
    <slot name="separator">
      <span class="separator">&ndash;</span>
    </slot>

    <PickerDate
      v-model="modelMax"
      :disabled="disableEndDateComp"
      :type="type"
      :format="format"
      :transfer="transfer"
      :disabled-date="dateRange.maxDisabled"
      :placeholder="maxPlaceholder || t('placeholder.endDate')"
      @on-change="onEndDateChange"
    />
  </div>
</template>


<style lang="less" scoped>
.date-range-picker {
  display: flex;
  align-items: center;
  min-width: 250px;

  .date-picker {
    flex: 1;
  }

  .separator {
    flex-shrink: 0;
    margin: 0 10px;
  }
}
</style>
