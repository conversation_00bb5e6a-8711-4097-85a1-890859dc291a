<script setup lang="ts">
import { getCurrentInstance, onMounted, toRef } from 'vue';

import { heightToTop } from '@/utils/dom';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';


interface Props {
  title: string;
  titleExtra?: string;
  order: number | null;
  navBar: NavigationBar | null;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  titleExtra: '',
  order: null,
  navBar: null,
});

const vm = getCurrentInstance();
const navBar = toRef(props, 'navBar');

function scrollIntoView() {
  vm.proxy.$el.scrollIntoView({
    behavior: 'smooth',
  });
}

function updateNavBar() {
  if (!navBar.value) {
    return;
  }

  navBar.value.push({
    title: props.title,
    order: props.order,
    scrollIntoView,
    getInfo(targetEle) {
      const offsetTop = heightToTop(vm.proxy.$el, targetEle);
      const { offsetHeight } = vm.proxy.$el;

      return {
        offsetTop,
        offsetHeight,
      };
    },
  });
}

onMounted(() => {
  updateNavBar();
});
</script>


<template>
  <div class="content-block">
    <div
      v-if="!$slots.customHeader"
      class="header-container"
    >
      <div class="header">
        <span class="title">{{ title }}</span>
        <slot name="header-right" />
      </div>

      <div
        v-if="titleExtra"
        class="title-extra"
      >
        <PimaSanitizeHtml
          :inner-html="titleExtra"
        />
      </div>
    </div>

    <slot
      v-else
      name="customHeader"
    />

    <div class="wrap">
      <slot />
    </div>
  </div>
</template>


<style lang="less" scoped>
.content-block {
  margin-bottom: 30px;

  .header-container {
    margin-bottom: 16px;
  }

  .header {
    display: flex;
    align-items: center;
    color: var(--theme-color);
    font-weight: bold;
    font-size: 18px;
    line-height: 25px;
  }

  .title-extra {
    margin-top: 2px;
    color: #8d8e8f;
    font-weight: 300;
    font-size: 14px;
    line-height: 20px;
    white-space: pre-wrap;
    word-break: break-word;
  }
}
</style>
