<template>
  <PimaModal
    :mask-closable="false"
    v-bind="$attrs"
    class="pima-modal-wrapper"
  >
    <div class="content">
      <Icon
        type="ios-alert-outline"
        class="icon-warnning"
      />
      <div class="description">
        {{ description }}
      </div>
    </div>

    <!-- <template #footer>
      <div class="action">
        <Button
          type="default"
          class="pima-btn"
          @click="onCancel()"
        >
          {{ t('common.action.cancel') }}
        </Button>
        <Button
          type="primary"
          class="button-primary pima-btn"
          :loading="loading"
          @click="onConfirm()"
        >
          {{ t('common.action.confirm') }}
        </Button>
      </div>
    </template> -->
  </PimaModal>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';
import PimaModal from './pima-modal.vue';


export default defineComponent({
  name: 'ModalWarnning',

  components: {
    PimaModal,
  },

  props: {
    description: {
      type: String,
      default: null,
    },
  },

  setup() {
    return {
    };
  },
});
</script>


<style lang="less" scoped>
:deep(.ivu-modal-body) {
  padding: 0;
}

:deep(.ivu-modal-content) {
  border-radius: 4px;
}

.content {
  display: flex;
  align-items: center;

  .icon-warnning {
    width: 44px;
    color: var(--primary-color);
    font-size: 44px;
  }

  .description {
    margin-left: 15px;
    color: fade(#000, 85%);
    font-weight: normal;
    font-size: 16px;
  }

  .action {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    .button-primary {
      width: 90px;
      line-height: 32px;
      border: none;
    }
  }
}
</style>
