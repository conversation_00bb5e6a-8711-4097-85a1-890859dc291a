import type { InternalAxiosRequestConfig } from 'axios';
import Cookies from 'js-cookie';

type LocaleInterceptorOptions = {
  localeCookieKey: string;
};

/**
 * 该函数用作请求拦截器，用于向请求参数中注入用户的语言偏好设置，该设置通常从Cookie中获取。
 *
 * @param {LocaleInterceptorOptions} options - 配置对象，期望包含一个localeCookieKey属性，表示存储语言偏好的Cookie键名。
 * @param  request - 一个代表请求的对象，该对象应包含params属性用于存放请求参数。
 * @returns {Function} 返回一个新的函数，该函数接受一个请求对象并返回一个修改后的请求对象，
 *                    其中包含了原有的请求参数及从Cookie中获取到的语言偏好参数。
 */
export function interceptorLocale({ localeCookieKey }: LocaleInterceptorOptions)
  : (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig {
  return (config: InternalAxiosRequestConfig) => {
    // 从Cookie中获取语言偏好设置
    const lang = Cookies.get(localeCookieKey);

    const params = {
      ...config.params,
      lang,
    };
    Object.assign(config, { params });

    return config;
  };
}
