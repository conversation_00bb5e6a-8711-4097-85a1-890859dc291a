<template>
  <div>
    <Button
      v-for="(item, index) of actions"
      :key="index"
      type="text"
      class="pima-btn"
      :disabled="item.disabled"
      :title="item.label"
      @click="onClick(item.triggerEvent)"
    >
      <img
        v-if="item.icon"
        :src="item.icon"
        class="icon"
      >
      <template v-else>
        {{ item.label }}
      </template>
    </Button>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'TableActionDefault',

  props: {
    actions: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  emits: ['trigger'],

  setup(props, { emit }) {
    function onClick(triggerEvent) {
      emit('trigger', triggerEvent);
    }

    return {
      onClick,
    };
  },
});
</script>
