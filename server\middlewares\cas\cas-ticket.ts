/* eslint-disable @typescript-eslint/naming-convention */
module.exports = function createCasTicketMiddlewares(options) {
  const { getRedisValue, setRedisValue, deleteRedisKey } = require('./redis')();
  const { getJsonFromXML } = require('./utils')();

  const {
    redisClient,
    casUserSessionKey,
    tokenSessionKey,
  } = options;
  const config_casUserSessionKey = casUserSessionKey;
  const config_tokenSessionKey = tokenSessionKey || 'appToken';

  const TICKET_PREFIX = 'tkt';
  const SESSION_PREFIX = 'sess';
  const DURATION = 60 * 60 * 24 * 30; // 保存30天，正常情况不会超过这个时间，实现自动清除

  if (!redisClient) {
    throw new Error('redisClient not configured');
  }

  if (!config_casUserSessionKey) {
    throw new Error('casUserSessionKey not configured');
  }

  function getTicketKey(ticket) {
    return `${TICKET_PREFIX}:${ticket}`;
  }

  function getSessionKey(sessionId) {
    return `${SESSION_PREFIX}:${sessionId}`;
  }

  async function casTicketSessionMapperMiddleware(req, res, next) {
    if (!req.xhr && req.query && req.query.ticket && req.session && req.session.id) {
      try {
        // 当有ticket参数时，清除用户登录相关session
        // eslint-disable-next-line camelcase
        if (req.session[config_casUserSessionKey]) {
          // eslint-disable-next-line no-param-reassign, camelcase
          delete req.session[config_casUserSessionKey];
        }

        // eslint-disable-next-line camelcase
        if (req.session[config_tokenSessionKey]) {
          // eslint-disable-next-line no-param-reassign, camelcase
          delete req.session[config_tokenSessionKey];
        }

        // 仅保存30天
        const key = getTicketKey(req.query.ticket);
        await setRedisValue(redisClient, key, req.session.id, DURATION); // 保存30天，正常情况不会超过这个时间，实现自动清除
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Save ticket and session id map error', error.stack || '');
        throw error;
      }
    }

    next();
  }

  async function casSloMiddleware(req, res, next) {
    if (req.body && req.body.logoutRequest) {
      const logoutRequest = await getJsonFromXML(req.body.logoutRequest);
      if (logoutRequest
        && logoutRequest['samlp:LogoutRequest']
        && logoutRequest['samlp:LogoutRequest']['samlp:SessionIndex']) {
        const sessionIndex = logoutRequest['samlp:LogoutRequest']['samlp:SessionIndex'];
        if (sessionIndex && Array.isArray(sessionIndex) && sessionIndex.length > 0) {
          const ticketKey = getTicketKey(sessionIndex[0].trim());
          const sessionId = await getRedisValue(redisClient, ticketKey);
          const sessionKey = getSessionKey(sessionId);
          await deleteRedisKey(redisClient, sessionKey);
          await deleteRedisKey(redisClient, ticketKey);
        }
      }

      res.sendStatus(201);
      return;
    }

    next();
  }

  return {
    casTicketSessionMapperMiddleware,
    casSloMiddleware,
  };
};
