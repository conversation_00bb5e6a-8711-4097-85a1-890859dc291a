<script setup lang="ts">
import { ref, computed, onBeforeMount, nextTick, watch } from 'vue';

import type { ActionLogType } from '^/types/action-log';

import { namespaceT } from '@/helps/namespace-t';
import { useActionLogStepStore } from '@/store/action-log/action-log-step';
import { useActionLogActionStore } from '@/store/action-log/action-log-action';

const props = withDefaults(defineProps<{
  dataSource: ActionLogType[];
  title?: string;
  emptyText?: string;
}>(), {
  title: '',
  emptyText: '',
});

const td = namespaceT('dateFormat');
const actionLogBodyRef = ref();

const current = computed(() => props.dataSource.length - 1);
const actionLogStepStore = useActionLogStepStore();
const actionLogActionStore = useActionLogActionStore();


onBeforeMount(() => {
  actionLogStepStore.loadDataIfNeeded();
  actionLogActionStore.loadDataIfNeeded();
});

watch(() => props.dataSource, (val) => {
  if (val) {
    nextTick(() => {
      if (actionLogBodyRef.value) {
        actionLogBodyRef.value.scrollTop = actionLogBodyRef.value.scrollHeight;
      }
    });
  }
}, {
  immediate: true,
});
</script>


<template>
  <div class="pima-action-log">
    <div class="pima-action-log-title">
      {{ title }}
    </div>
    <div
      ref="actionLogBodyRef"
      class="pima-action-log-body"
    >
      <Steps
        :current="current"
        direction="vertical"
      >
        <Step
          v-for="(item, idx) in dataSource"
          :key="`action-log${item.id}`"
          :title="actionLogStepStore.getTextByCode(item.userType)"
        >
          <template #content>
            <div class="name-action">
              {{ item.userName }}

              <span class="ml-4">
                {{ actionLogActionStore.getTextByCode(item.action) }}
              </span>
            </div>
            <div class="action-time">
              {{ $dateFormatSTZ(item.actionTime, td('fullDateTime')) }}
            </div>
            <div
              v-if="item.remark"
              class="remark"
            >
              {{ item.remark }}
            </div>
          </template>

          <template #icon>
            <img
              v-if="current === idx"
              src="@/assets/img/icon/icon-step-active.svg"
            >
            <img
              v-else
              src="@/assets/img/icon/icon-step-finished.svg"
            >
          </template>
        </Step>

        <span
          v-if="dataSource.length===0"
          class="empty-text"
        >
          {{ emptyText }}
        </span>
      </Steps>
    </div>
  </div>
</template>

<style lang="less" scoped>
.pima-action-log{
  .empty-text{
    color: #8d8e8f;
    font-weight: 400;
    font-size: 14px;
  }

}
</style>
