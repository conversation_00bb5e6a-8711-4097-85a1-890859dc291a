import { App } from 'vue';
import { SStore, LStore } from '@/utils/web-store';
import eventBus from '@/utils/event-bus';
import { textByLocale } from '@/helps/locale';
import { dateFormat, dateFormatSTZ } from '@/helps/date';

export const setupInjectPrototypePlugin = (app: App) => {
  Object.assign(app.config.globalProperties, {
    $eventBus: eventBus,
    $dateFormatSTZ: dateFormatSTZ,
    $dateFormat: dateFormat,
    $LStore: LStore,
    $SStore: SStore,
    $judgeLanguage: textByLocale,
  });
};
