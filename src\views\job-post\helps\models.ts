import type { SearchSimpleModelType } from '^/types/job-post';

import { namespaceT } from '@/helps/namespace-t';
import { createWarningModel } from '@/helps/models';

export const createSearchSimpleModel = (): SearchSimpleModelType => {
  return {
    keyword: undefined,
    creStartTime: undefined,
    creEndTime: undefined,
    status: undefined,
    publishStatus: undefined,
  };
};


export const createDeleteModel = () => {
  const t = namespaceT('jobPost.modal.delete');

  return createWarningModel({
    title: t('title'),
    content: t('content'),
  });
};


export const createFormModel = () => {
  return {
    name1: [],
  };
};
