<script setup lang="ts">
import { ref, watch } from 'vue';
import Paginator from '@/components/common/paginator.vue';


interface Props {
  queryTable: {
    table: {
      total: number;
    };
    page: {
      page: number;
      limit: number;
    };
    turnPage: (page: number, limit: number) => void;
  };
  simple?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  queryTable: undefined,
  simple: false,
});


const qt = ref(props.queryTable);


watch(
  () => props.queryTable,
  (newVal) => {
    qt.value = newVal;
  },
);
</script>


<template>
  <Paginator
    v-if="qt.table.total > 0"
    v-model:page-index="qt.page.page"
    v-model:page-size="qt.page.limit"
    :total="qt.table.total"
    :simple="simple"
    @change="qt.turnPage"
  />
</template>
