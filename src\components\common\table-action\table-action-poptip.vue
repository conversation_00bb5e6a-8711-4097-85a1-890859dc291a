<template>
  <div>
    <Button
      v-for="(item, index) of externalAction"
      :key="`external-action-${index}`"
      type="text"
      class="pima-btn"
      :disabled="item.disabled"
      :title="item.label"
      @click="onClick(item.triggerEvent)"
    >
      <img
        v-if="item.icon"
        :src="item.icon"
        class="icon"
      >
      <template v-else>
        {{ item.label }}
      </template>
    </Button>

    <Poptip
      v-model="poptipShown"
      trigger="hover"
      placement="left"
      :transfer="true"
      :width="60"
      :popper-class="`pima-table-action-poptip-wrapper ${textBtn ? 'text-btn' : ''}`"
    >
      <i class="pima-table-action-more" />
      <template #content>
        <ButtonGroup vertical>
          <Button
            v-for="(item, index) of internalAction"
            :key="`internal-action-${index}`"
            type="text"
            class="pima-btn"
            :disabled="item.disabled"
            :title="item.label"
            @click="onClick(item.triggerEvent)"
          >
            <img
              v-if="item.icon"
              :src="item.icon"
              class="icon"
            >
            <template v-else>
              {{ item.label }}
            </template>
          </Button>
        </ButtonGroup>
      </template>
    </Poptip>
  </div>
</template>


<script lang='ts'>
import { computed, defineComponent, ref, toRef, unref } from 'vue';

export default defineComponent({
  name: 'TableActionPoptip',

  props: {
    actions: {
      type: Array,
      default() {
        return [];
      },
    },

    limit: {
      type: Number,
      required: true,
    },

    textBtn: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['trigger'],

  setup(props, { emit }) {
    const poptipShown = ref(false);

    const limitOfProps = toRef(props, 'limit');
    const actionsOfProps = toRef(props, 'actions');

    function onClose() {
      poptipShown.value = false;
    }

    function onClick(triggerEvent) {
      onClose();
      emit('trigger', triggerEvent);
    }

    const externalAction = computed(() => unref(actionsOfProps).slice(0, unref(limitOfProps) - 1));
    const internalAction = computed(() => unref(actionsOfProps).slice(unref(limitOfProps) - 1));

    return {
      poptipShown,
      externalAction,
      internalAction,

      onClick,
    };
  },
});
</script>


<style lang='less'>
.pima-table-action-poptip-wrapper{
  .ivu-btn-group.ivu-btn-group-vertical{
    .ivu-btn.pima-btn{
      border-radius: 8px;
    }
  }
}

</style>
