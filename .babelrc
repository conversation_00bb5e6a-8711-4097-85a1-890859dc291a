{"presets": [["@babel/preset-env", {"modules": false, "useBuiltIns": "entry", "corejs": 3}], ["@babel/preset-typescript", {"allExtensions": true}]], "comments": false, "plugins": ["@babel/plugin-syntax-dynamic-import", ["@babel/plugin-transform-runtime", {"corejs": 3, "helpers": true, "regenerator": true, "useESModules": true}], "@babel/plugin-transform-modules-commonjs", "@babel/plugin-transform-object-rest-spread"]}