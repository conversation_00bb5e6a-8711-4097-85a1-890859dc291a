<script lang='ts' setup>
import { getCurrentInstance, ref, provide, onBeforeMount, nextTick } from 'vue';

// eslint-disable-next-line import/order
import TheTheme from './the-theme.vue';
// eslint-disable-next-line import/order
import TheLayout from './the-layout.vue';
// eslint-disable-next-line import/order
import TheHeader from './the-header.vue';
// eslint-disable-next-line import/order
import TheSider from './the-sider.vue';

// eslint-disable-next-line import/order
import ModalAccountError from './biz/modal-account-error.vue';

import { MENU_NAME_INJECTION_KEY } from '@/config/sider-menu';
import { KeepAliveInclude, KeepAliveExclude } from '@/config/keep-alive';
import { PUBLIC_PATH } from '@/config/public-path';
import { useSider } from '@/uses/sider';
import emitter from '@/utils/event-bus';
import { getLocale } from '@/helps/locale';


const vm = getCurrentInstance();
const locale = getLocale();
const sider = useSider();
const showView = ref(true);
const accountErrorCode = ref('');
const isShowModalAccountError = ref(false);


provide(MENU_NAME_INJECTION_KEY, sider.getMenuName);


function handleLogout() {
  const redirect = encodeURIComponent(window.location.href);
  const url = `${PUBLIC_PATH}logout?service=${redirect}`;
  window.location.replace(url);
}

function bindEvents() {
  // 公共头部聚合了初始数据，在这里监听数据获取情况
  emitter.on('userDataFetched', ({ error, operations }) => {
    if (error) {
      return;
    }

    vm.proxy.$setAuths(operations);
  });


  const reload = () => {
    window.location.reload();
  };

  // 如果接口出现用户未认证错误，则刷新界面
  emitter.on('accountUnauthorizedError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('accessTokenInvalidError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('refreshTokenInvalidError', () => {
    handleLogout();
  });

  emitter.on('accountDisabledError', (error) => {
    accountErrorCode.value = error.code;
    isShowModalAccountError.value = true;
  });
}

function rebuildView() {
  showView.value = false;
  nextTick(() => {
    showView.value = true;
  });
}

onBeforeMount(() => {
  bindEvents();
});

</script>


<template>
  <TheTheme :locale="locale">
    <TheLayout :has-sider="sider.isShow.value">
      <template #header>
        <TheHeader @on-logout="handleLogout" />
      </template>

      <template #sider>
        <TheSider
          v-show="sider.isShow.value"
          @rebuild="rebuildView()"
        />
      </template>

      <template #main>
        <RouterView
          v-if="$hasAuths() && showView"
          v-slot="{ Component, route }"
        >
          <KeepAlive
            :include="KeepAliveInclude"
            :exclude="KeepAliveExclude"
          >
            <component
              :is="Component"
              :key="route.fullPath"
            />
          </KeepAlive>
        </RouterView>
      </template>
    </TheLayout>

    <!-- 账户出错Modal窗 -->
    <ModalAccountError
      v-model="isShowModalAccountError"
      :account-error-code="accountErrorCode"
      @on-logout="handleLogout"
    />
  </TheTheme>
</template>
