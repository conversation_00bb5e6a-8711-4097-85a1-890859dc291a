import { Table } from 'view-ui-plus';


const PREFIX_CLS = 'ivu-table';

export default {
  name: 'CTable',

  extends: Table,

  computed: {
    localeNoDataText() {
      if (this.loading) {
        return '';
      }

      const noDataText = this.noDataText || this.t('i.table.noDataText');
      return `<span class="pima-table-no-data-text-wrapper">
  <span class="no-data-text">${noDataText}</span>
</span>`;
    },

    wrapClasses() {
      return [
        `${PREFIX_CLS}-wrapper`,
        {
          [`${PREFIX_CLS}-hide`]: !this.ready,
          [`${PREFIX_CLS}-with-header`]: this.showSlotHeader,
          [`${PREFIX_CLS}-with-footer`]: this.showSlotFooter,
          [`${PREFIX_CLS}-with-summary`]: this.showSummary,
          [`${PREFIX_CLS}-wrapper-with-border`]: this.border,
        },
        'pima-table-wrapper',
      ];
    },
  },
};
