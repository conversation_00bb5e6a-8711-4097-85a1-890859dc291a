export const SStore = {
  get<T>(key: string) {
    const s = sessionStorage.getItem(key);
    const o = JSON.parse(s) as T;
    return o;
  },

  set<T>(key: string, value: T) {
    const s = JSON.stringify(value);
    sessionStorage.setItem(key, s);
  },

  has(key: string) {
    const value = SStore.get(key);
    return ![null, undefined].includes(value);
  },

  remove(key: string) {
    sessionStorage.removeItem(key);
  },
};

export const LStore = {
  get<T>(key: string) {
    const s = localStorage.getItem(key);
    const o = JSON.parse(s) as T;
    return o;
  },

  set<T>(key: string, value: T) {
    const s = JSON.stringify(value);
    localStorage.setItem(key, s);
  },

  has(key: string) {
    const value = LStore.get(key);
    return ![null, undefined].includes(value);
  },

  remove(key: string) {
    localStorage.removeItem(key);
  },
};
