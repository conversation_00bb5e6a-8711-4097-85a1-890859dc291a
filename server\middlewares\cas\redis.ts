module.exports = function createRedisUtils() {
  function getRedisValue(redisClient, key) {
    return new Promise((resolve, reject) => {
      redisClient.get(key, (error, value) => {
        if (!error) {
          resolve(value);
        } else {
          // eslint-disable-next-line no-console
          console.error('Get redis key error', key, error);
          reject(error);
        }
      });
    });
  }

  function setRedisValue(redisClient, key, value, ttl) {
    return new Promise((resolve, reject) => {
      redisClient.set(key, value, 'EX', ttl, (error, returnValue) => {
        if (!error) {
          resolve(returnValue);
        } else {
          // eslint-disable-next-line no-console
          console.error('Set redis key error', key, value, ttl, error);
          reject(error);
        }
      });
    });
  }

  function deleteRedisKey(redisClient, key) {
    return new Promise<void>((resolve, reject) => {
      redisClient.del(key, (error) => {
        if (!error) {
          resolve();
        } else {
          // eslint-disable-next-line no-console
          console.error('Delete redis key error', key, error);
          reject(error);
        }
      });
    });
  }

  return {
    getRedisValue,
    setRedisValue,
    deleteRedisKey,
  };
};
