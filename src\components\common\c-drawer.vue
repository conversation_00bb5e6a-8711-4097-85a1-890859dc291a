<template>
  <Drawer
    v-bind="$attrs"
    :class-name="themeClass"
    :mask-closable="false"
    :closable="false"
    :width="width"
    :model-value="modelValue"
  >
    <template v-if="showContent">
      <div class="pima-drawer-title">
        {{ title }}
      </div>

      <div
        v-if="loading"
        class="loading"
      >
        <Spin
          size="large"
          fix
        />
      </div>
      <template v-else>
        <div class="pima-drawer-body">
          <slot />
        </div>
      </template>

      <div class="pima-button-close">
        <Icon
          size="26"
          type="md-close"
          @click="$emit('on-close')"
        />
      </div>
    </template>
  </Drawer>
</template>

<script>
import {
  computed,
  defineComponent,
  inject,
  ref,
  toRef,
  unref,
  watch,
  nextTick,
} from 'vue';
import { Spin } from 'view-ui-plus';

import { Locale } from '@/config/locale';


export default defineComponent({
  name: 'CDrawer',

  components: {
    Spin,
  },

  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },

    width: {
      type: Number,
      default: 960,
    },

    title: {
      type: String,
      default: '',
    },

    loading: {
      type: Boolean,
      default: false,
    },

    contentRebuild: {
      type: Boolean,
      default: false,
    },

    className: {
      type: String,
      default: '',
    },
  },

  emits: ['on-close'],

  setup(props) {
    const showContent = ref(true);
    const drawerShown = toRef(props, 'modelValue');
    const contentRebuildOfProps = toRef(props, 'contentRebuild');

    const locale = inject('locale');

    function rebuildContent() {
      if (unref(contentRebuildOfProps)) {
        showContent.value = false;
        nextTick(() => {
          showContent.value = true;
        });
      }
    }

    function getThemeClass(currLang) {
      const classList = ['pima-drawer-wrapper'];
      if (currLang && Object.values(Locale).indexOf(currLang) !== -1) {
        if (currLang === Locale.ZH_MO) {
          classList.push('pima-chn-lang-theme');
        } else {
          classList.push('pima-eng-lang-theme');
        }
      }

      if (props.className) {
        classList.push(props.className);
      }

      return classList.join(' ');
    }

    const themeClass = computed(() => getThemeClass(unref(locale)));

    watch(drawerShown, (val) => {
      if (val) {
        rebuildContent();
      }
    });

    return {
      showContent,
      themeClass,
    };
  },
});
</script>
