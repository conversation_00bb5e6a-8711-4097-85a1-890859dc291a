<template>
  <div class="pima-drawer-form-wrapper">
    <template
      v-if="is(ContentType.DEFAULT)"
    >
      <div class="pima-drawer-content-main">
        <slot />
      </div>
      <div
        v-if="$slots.action"
        class="pima-drawer-content-footer"
      >
        <div class="h-center mt-40 mb-50 gap-list-30">
          <slot name="action" />
        </div>
      </div>
    </template>

    <slot
      v-else-if="is(ContentType.SUCCESSED)"
      name="successed"
    />

    <slot
      v-else-if="is(ContentType.FAILED)"
      name="failed"
    />

    <Spin
      v-if="loading"
      size="large"
      fix
    />
  </div>
</template>


<script>
import { namespaceT } from '@/helps/namespace-t';


const ContentType = Object.freeze({
  DEFAULT: 0, // 默认内容
  SUCCESSED: 1, // 成功
  FAILED: 2, // 失败
});

export const DrawerFormWrapperContentType = ContentType;

export default {
  name: 'DrawerFormWrapper',

  props: {
    loading: {
      type: Boolean,
      default: false,
    },

    contentType: {
      type: Number,
      default: ContentType.DEFAULT,
    },
  },

  setup(props) {
    function is(contentType) {
      return props.contentType === contentType;
    }

    return {
      ContentType,

      is,
      t: namespaceT('common'),
    };
  },
};
</script>
