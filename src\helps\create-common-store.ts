import { defineStore } from 'pinia';
import type { StateTree } from 'pinia';
import _ from 'lodash';

export function createCommonStore(storeName, DataTagApi, options: {
  state?: StateTree; getters?: StateTree; actions?: StateTree;
} = {}) {
  const initState = {
    data: null,
    loading: false,
    loaded: false,

    ...options.state,
  };

  const getters = {
    getTextByCode(state) {
      return function getTextByCode(code) {
        const match = _.find(state.data || [], { code });
        return match ? match.nameByLocale : null;
      };
    },

    ...options.getters,
  };

  const actions = {
    async loadDataIfNeeded() {
      if (this.loading || this.loaded || this.data !== null) {
        return;
      }

      this.loading = true;
      try {
        const api = new DataTagApi();
        const data = await api.send();
        this.data = data;
      } catch (error) {
        this.data = null;
        throw error;
      } finally {
        this.loading = false;
        this.loaded = true;
      }
    },

    ...options.actions,
  };

  return defineStore(storeName, {
    state: () => initState,
    actions,
    getters,
  });
}
