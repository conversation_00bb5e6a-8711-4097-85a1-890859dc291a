<template>
  <div class="pima-search-advanced-wrapper">
    <div class="content">
      <slot />
    </div>

    <ol class="gap-list-15 operation">
      <li>
        <Button
          type="primary"
          class="pima-btn"
          @click="emitSearch"
        >
          {{ t('common.action.search') }}
        </Button>
      </li>

      <li>
        <Button
          type="default"
          class="pima-btn"
          @click="emitReset"
        >
          {{ t('common.action.reset') }}
        </Button>
      </li>

      <li>
        <Button
          type="default"
          class="pima-btn"
          @click="emitShowSimpleSearch"
        >
          {{ t('common.action.cancel') }}
        </Button>
      </li>
    </ol>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';
import { useI18n } from 'vue-i18n';


export default defineComponent({
  name: 'WrapperSearchAdvanced',

  emits: ['search', 'reset', 'show-search-simple'],

  setup(props, { emit }) {
    const { t } = useI18n();

    const emitSearch = () => {
      emit('search');
    };

    const emitReset = () => {
      emit('reset');
    };

    const emitShowSimpleSearch = () => {
      emit('show-search-simple');
    };

    return {
      t,
      emitSearch,
      emitReset,
      emitShowSimpleSearch,
    };
  },
});
</script>
