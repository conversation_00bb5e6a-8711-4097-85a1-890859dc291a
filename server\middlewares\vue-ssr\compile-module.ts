module.exports = (files, basedir, runInNewContext) => {
  const NativeModule = require('module');
  // eslint-disable-next-line import/no-extraneous-dependencies
  const resolve = require('resolve');
  const vm = require('vm');
  const path = require('path');


  const compiledScripts = {};
  const resolvedModules = {};
  function getCompiledScript(filename) {
    if (compiledScripts[filename]) {
      return compiledScripts[filename];
    }
    const code = files[filename];
    const wrapper = NativeModule.wrap(code);
    const script = new vm.Script(wrapper, {
      filename,
      displayErrors: true,
    });
    compiledScripts[filename] = script;
    return script;
  }
  function evaluateModule(filename, sandbox, evaluatedFiles = {}) {
    if (evaluatedFiles[filename]) {
      return evaluatedFiles[filename];
    }

    const script = getCompiledScript(filename);
    const compiledWrapper = runInNewContext === false
      ? script.runInThisContext()
      : script.runInNewContext(sandbox);
    const m = { exports: {} };
    // eslint-disable-next-line consistent-return
    const r = (file) => {
      // eslint-disable-next-line no-param-reassign
      file = path.posix.join('.', file);
      if (files[file]) {
        return evaluateModule(file, sandbox, evaluatedFiles);
      } if (basedir) {
        // eslint-disable-next-line no-return-assign, import/no-dynamic-require
        return require(resolvedModules[file]
          || (resolvedModules[file] = resolve.sync(file, { basedir })));
      }
      // eslint-disable-next-line import/no-dynamic-require
      return require(file);
    };
    compiledWrapper.call(m.exports, m.exports, r, m);
    const res = Object.prototype.hasOwnProperty.call(m.exports, 'default')
      // @ts-expect-error: Property 'default' does not exist on type '{}'.ts(2339)
      ? m.exports.default
      : m.exports;
    // eslint-disable-next-line no-param-reassign
    evaluatedFiles[filename] = res;

    return res;
  }

  return evaluateModule;
};
