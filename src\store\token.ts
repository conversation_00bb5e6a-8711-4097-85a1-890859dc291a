import { ref } from 'vue';
import { defineStore } from 'pinia';
import { tokenService } from '@/services/token';


export const useTokenStore = defineStore('token', () => {
  const loading = ref(false);
  const loaded = ref(false);
  const tokenType = ref<string>();
  const accessToken = ref<string>();
  const expiresIn = ref<Date>();
  const refreshToken = ref<string>();
  let loadDataPromise: Promise<void>;


  async function loadData(): Promise<void> {
    loading.value = true;
    try {
      const data = await tokenService.getToken();
      tokenType.value = data.tokenType;
      accessToken.value = data.accessToken;
      expiresIn.value = data.expiresIn;
      refreshToken.value = data.refreshToken;
      loaded.value = true;
    } catch (err) {
      loaded.value = false;
      throw err;
    } finally {
      loading.value = false;
      loadDataPromise = null;
    }
  }

  async function loadDataIfNeeded(): Promise<void> {
    if (loaded.value) {
      return;
    }

    if (loading.value) {
      await loadDataPromise;
      return;
    }

    loadDataPromise = loadData();
    await loadDataPromise;
  }

  function getToken() {
    return `${tokenType.value} ${accessToken.value}`;
  }

  return {
    accessToken,
    expiresIn,
    refreshToken,

    loadDataIfNeeded,
    getToken,
  };
});
