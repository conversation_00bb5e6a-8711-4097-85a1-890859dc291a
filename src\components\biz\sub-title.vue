<script setup lang="ts">
defineProps<{
  title: string;
}>();
</script>


<template>
  <div class="content-block">
    <div class="header">
      <div class="title">
        {{ title }}
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
@paddingTop: 8px;
@headerBorderWidth: 1px;

.content-block {
  padding-top: 12px;
  padding-left: 24px;

  .header {
    display: flex;
    align-items: center;
    padding: @paddingTop 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 20px;
    line-height: 22px;
    border-bottom: 1px solid #efefef;

    .title {
      position: relative;
      left: 28px;

      &::after {
        position: absolute;
        bottom: -(@paddingTop + @headerBorderWidth);
        left: 0;
        width: 100%;
        height: 2px;
        background: var(--primary-color);
        content: "";
      }
    }
  }
}
</style>
