<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Panzoom, { PanzoomEventDetail, PanzoomObject } from '@panzoom/panzoom';

import { namespaceT } from '@/helps/namespace-t';


interface PanzoomzoomEvent extends Event {
  detail: PanzoomEventDetail
}

type Props = Partial<{
  minSize: number;
  maxSize: number;
  interval: number;
}>;

const porps = withDefaults(defineProps<Props>(), {
  minSize: 12,
  maxSize: 500,
  interval: 10,
});
const emits = defineEmits<{
  'on-zoom-in': [size: number],
  'on-zoom-out': [size: number],
  'on-reset': [],
}>();

let mountEl: HTMLElement;
let panzoom: PanzoomObject;

const defaultSize = 100;
const t = namespaceT('processDefinition.zoom');
const size = ref(defaultSize);
const mountId = 'zoom-content';

function zoomIn(): void {
  panzoom.zoomIn();
  emits('on-zoom-in', size.value);
}

function zoomOut(): void {
  panzoom.zoomOut();
  emits('on-zoom-out', size.value);
}

function reset(): void {
  panzoom?.reset();
  size.value = defaultSize;
}

function onClickReset(): void {
  reset();
  emits('on-reset');
}

function initPanzoom(): void {
  mountEl = document.getElementById(mountId);
  panzoom = Panzoom(mountEl, {
    maxScale: porps.maxSize / 100,
    minScale: porps.minSize / 100,
  });
}

function handlePanzoomzoomEvent(event: PanzoomzoomEvent): void {
  size.value = Math.floor(event.detail.scale * 100);
}

function handleWheel(event: WheelEvent): void {
  panzoom.zoomWithWheel(event);
}

function bindEvents(): void {
  mountEl.addEventListener('wheel', handleWheel);
  mountEl.addEventListener('panzoomzoom', handlePanzoomzoomEvent);
}

onMounted(() => {
  initPanzoom();
  bindEvents();
});

defineExpose({ zoomIn, zoomOut, reset });
</script>


<template>
  <div
    class="zoom-wrapper"
  >
    <div class="zoom-bar">
      <div class="zoom-block">
        <Button
          type="text"
          icon="md-remove"
          :title="t('action.zoomOut')"
          :disabled="size === minSize"
          @click="zoomOut()"
        />

        <span class="zoom-size">{{ size }}%</span>

        <Button
          type="text"
          icon="md-add"
          :disabled="size === maxSize"
          :title="t('action.zoomIn')"
          @click="zoomIn()"
        />

        <Button
          type="primary"
          ghost
          class="pima-btn ml-5"
          @click="onClickReset()"
        >
          {{ $t('common.action.reset') }}
        </Button>
      </div>
    </div>

    <div class="zoom-content-wrap">
      <div
        :id="mountId"
        class="zoom-content"
      >
        <slot />
      </div>
    </div>
  </div>
</template>


<style scoped lang="less">
.zoom-wrapper {
  width: 100%;
  height: 100%;

  .zoom-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 10px;

    .zoom-block {
      background-color: #fcfcfc;
      border: 1px solid #e8eaec;

      .zoom-size {
        display: inline-block;
        width: 40px;
        margin: 0 3px;
        text-align: center;
      }

      :deep(.ivu-btn-ghost) {
        color: #515a5e !important;
        background-color: transparent !important;
        border-color: transparent;
        border-left-color: #e8eaec;

        &:hover {
          color: #1f65e0 !important;
          background-color: #fff !important;
          border-color: transparent !important;
          border-left-color: #e8eaec !important;
        }
      }

      :deep(.ivu-btn-text:focus),
      :deep(.ivu-btn-primary:focus) {
        box-shadow: none;
      }
    }
  }

  .zoom-content-wrap {
    width: 100%;
    height: calc(100vh - 40px - 50px - 44px);
  }
}
</style>
