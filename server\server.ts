// @ts-expect-error: Cannot redeclare block-scoped variable 'process'.ts(2451)
const process = require('process');
const express = require('express');
const path = require('path');
const url = require('url');
const logger = require('morgan');
const helmet = require('helmet');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const Redlock = require('redlock');
const session = require('express-session');

const readEnvConfig = require('./env-config');

readEnvConfig();

const createRedisClient = require('./redis-client');
const createRedisStore = require('./redis-store');
const createPass = require('./pass');
const createConfig = require('./config');
const createProxyTable = require('./config/proxy-table');
const createPublicPathFixMiddleware = require('./middlewares/public-path-fix');
const createLocaleMiddleware = require('./middlewares/locale');
const createUnlessMiddleware = require('./middlewares/unless');
const createCASMiddlewares = require('./middlewares/cas');
const createProxyMiddlewares = require('./middlewares/proxy');
const createVueSSRMiddleware = require('./middlewares/vue-ssr');
const createXXssProtectionMiddleware = require('./middlewares/x-xss-protection-header');

const config = createConfig();
const {
  COOKIE_SECRET,
  REDIS_KEY_PREFIX,
  REDIS_TTL,
  SESSION_ID_COOKIE_KEY,
  LOCALE_COOKIE_KEY,
  FALLBACK_LOCALE,
  PUBLIC_PATH,
  HOT_CLIENT_WEB_SOCKET_PORT,
  LOGIN_STATUS_LOGGED_IN_KEY,
  API_SALT,
} = config;

const {
  createCasAuthentication,
  createCasTicketMiddlewares,
  createTokenMiddlewares,
  createLoginStatusMiddleware,
  createLogoutCheckerMiddleware,
} = createCASMiddlewares();
const {
  setXssProtectionHeader,
  xXssProtectionMiddleware,
} = createXXssProtectionMiddleware();
const pass = createPass({ publicPath: PUBLIC_PATH });

const isProd = process.env.NODE_ENV === 'production';
const expressPort = process.env.EXPRESS_PORT || 8080;
const localhostUrl = (expressPort === 80) ? 'http://localhost' : `http://localhost:${expressPort}`;
const serviceUrlString = process.env.SERVICE_URL || localhostUrl;
const serviceUrl = new url.URL(serviceUrlString);
const cookieSecure = (serviceUrl.protocol === 'https:');
const cookieSameSite = cookieSecure ? 'strict' : 'lax';

function resolve(...args) {
  return path.resolve(__dirname, ...args);
}

// 当收到未捕获的异常时，退出进程
process.on('uncaughtException', (error) => {
  // 记录未捕获的异常错误日志
  // eslint-disable-next-line no-console
  console.error('UncaughtException', error);
  // 手动退出进程，pm2会自动重启
  process.exit(1);
});

const app = express();
// 如果使用HTTPS，需要设置X-Forwarded-Proto头，防止express-session中的Cookie解密失败
if (cookieSecure) {
  app.set('trust proxy', 1); // trust first proxy
  app.use((req, res, next) => {
    // eslint-disable-next-line no-param-reassign
    req.headers['x-forwarded-proto'] = 'https';
    next();
  });
}

app.use(logger('combined'));
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false,
  crossOriginResourcePolicy: false,
}));

const staticOptions = { setHeaders: setXssProtectionHeader };
if (isProd) {
  app.use(`${PUBLIC_PATH}favicon.ico`, express.static(resolve('../public/favicon.ico'), staticOptions));
  app.use(`${PUBLIC_PATH}student-favicon.ico`, express.static(resolve('../public/student-favicon.ico'), staticOptions));
  app.use(`${PUBLIC_PATH}static/`, express.static(resolve('../dist/static/'), staticOptions));
} else {
  app.use(express.static(resolve('../src/assets'), staticOptions));
}

app.use(cookieParser(COOKIE_SECRET));
app.use(bodyParser.urlencoded({ limit: '2gb', extended: true }));
app.use(bodyParser.json({ limit: '2gb' }));

// XXS Protection
app.get('*', xXssProtectionMiddleware);

// 修复根问题路径问题，如果访问地址为不带 / 的，自动重定向至带 / 的地址
const publicPathFixMiddleware = createPublicPathFixMiddleware({
  originPublicPath: PUBLIC_PATH,
});
app.get('*', publicPathFixMiddleware);

// Redis
const initRedisClientOptions = {
  redisKeyPrefix: REDIS_KEY_PREFIX,
};
const redisClient = createRedisClient(initRedisClientOptions);

const initRedisStoreOptions = {
  redisClient,
  redisTtl: REDIS_TTL,
};
const redisStore = createRedisStore(initRedisStoreOptions);

const sessionOptions = {
  proxy: true,
  store: redisStore,
  secret: COOKIE_SECRET,
  resave: false,
  saveUninitialized: false,
  name: SESSION_ID_COOKIE_KEY,
  cookie: {
    secure: cookieSecure,
    httpOnly: true,
    sameSite: cookieSameSite,
  },
};
app.use(session(sessionOptions));

// 语言中间件
const localeMiddlewareOptions = {
  cookieSecure,
  cookieSameSite,
  localeCookieName: LOCALE_COOKIE_KEY,
  fallbackLocale: FALLBACK_LOCALE,
  debug: false,
};
const localeMiddleware = createLocaleMiddleware(localeMiddlewareOptions);
app.use(localeMiddleware);

// CAS
const CASAuthentication = createCasAuthentication();
const cas = new CASAuthentication({
  cas_url: process.env.CAS_BASE_URL,
  // cas_version: process.env.CAS_VERSION,
  service_url: serviceUrlString,
  session_info: 'cas_attributes',
});

// 分布式锁
const redlock = new Redlock(
  // you should have one client for each independent redis node
  // or cluster
  [redisClient],
  {
    // the expected clock drift; for more details
    // see http://redis.io/topics/distlock
    driftFactor: 0.01, // multiplied by lock ttl to determine drift time

    // the max number of times Redlock will attempt
    // to lock a resource before erroring
    retryCount: 10,

    // the time in ms between attempts
    retryDelay: 200, // time in ms

    // the max time in ms randomly added to retries
    // to improve performance under high contention
    // see https://www.awsarchitectureblog.com/2015/03/backoff.html
    retryJitter: 200, // time in ms
  },
);

// 在CAS认证之前，把Ticket与SessionID做对照，在CAS SLO时，执行删除
const casTicketMiddlewareOptions = {
  redisClient,
  casUserSessionKey: cas.session_name,
};
const { casTicketSessionMapperMiddleware, casSloMiddleware } = createCasTicketMiddlewares(casTicketMiddlewareOptions);
app.use(casTicketSessionMapperMiddleware);
app.post('*', casSloMiddleware);

// Token中间件
const tokenMiddlewareOptions = {
  redlock,
  clientId: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  baseUrl: process.env.BDC_CORE_API_BASE_URL,
  casUserSessionKey: cas.session_name,
  redisPrefix: REDIS_KEY_PREFIX,
  redisClient,
  apiSalt: API_SALT,
};
const logoutCheckerMiddleware = createLogoutCheckerMiddleware({
  serviceUrl: serviceUrlString,
});
const {
  blockTokenMiddleware,
  renewTokenMiddleware,
  replaceRequestTokenMiddleware,
  replaceLocalsTokenMiddleware,
  removeTokenMiddleware,
  tokenInfoMiddleware,
} = createTokenMiddlewares(tokenMiddlewareOptions);
app.get(`${PUBLIC_PATH}logout`, logoutCheckerMiddleware, removeTokenMiddleware, cas.logout); // CAS登出
app.use(createUnlessMiddleware(pass, blockTokenMiddleware)); // 当用户CAS身份失效时执行，返回401状态码
app.use(createUnlessMiddleware(pass, cas.bounce)); // CAS登入
app.use(createUnlessMiddleware(pass, renewTokenMiddleware)); // 刷新Token
app.use(createUnlessMiddleware(pass, replaceRequestTokenMiddleware)); // 替换AJAX请求Token
app.use(createUnlessMiddleware(pass, replaceLocalsTokenMiddleware)); // 替换express的locals变量
app.get(`${PUBLIC_PATH}x-token-info`, tokenInfoMiddleware); // Token信息

// 登录状态中间件
const loginStatusMiddleware = createLoginStatusMiddleware({
  casUserSessionKey: cas.session_name,
  loggedInKey: LOGIN_STATUS_LOGGED_IN_KEY,
});
app.use(createUnlessMiddleware(pass, loginStatusMiddleware));

// HTTP代理中间件
const proxyMiddlewareOptions = { proxyTable: createProxyTable(config) };
const proxyMiddlewares = createProxyMiddlewares(proxyMiddlewareOptions);
app.use(proxyMiddlewares);

// UEditor上传配置，部分项目没有UEditor后端对应接口，故做一个空接口
app.get(`${PUBLIC_PATH}x-upload-config`, (req, res) => {
  res.send('{}');
});

// vue-ssr中间件
const vueSSRMiddleware = createVueSSRMiddleware(app, {
  isProd,
  publicPath: PUBLIC_PATH,
  webpackClientConfig: (isProd ? require('../build/webpack.client.prod') : require('../build/webpack.client.dev')),
  webpackServerConfig: (isProd ? require('../build/webpack.server.prod') : require('../build/webpack.server.dev')),
  hotClientWebSocketPort: HOT_CLIENT_WEB_SOCKET_PORT,
  templatePath: resolve('../index.template.html'),
  vueSSRServerBundlePath: resolve('../dist/vue-ssr-server-bundle.json'),
  vueSSRClientManifestPath: resolve('../dist/vue-ssr-client-manifest.json'),
});
app.get('*', createUnlessMiddleware(pass, vueSSRMiddleware));

// 错误处理
app.use((error, req, res, next) => {
  // 以下三种情况不需要处理
  // 1、没有出错
  // 2、AJAX请求
  // 3、不是GET请求
  if (!error
    || req.xhr
    || req.method.toLowerCase() !== 'get') {
    next();
    return;
  }

  switch (error.code) {
    case 'ROUTE_NOT_FOUND':
      // eslint-disable-next-line no-console
      console.error(new Date().toISOString(), 'errorHandler', error);
      res.status(404).send('Page not found');
      break;

    case 'LOGIN_REQUIRED':
    case 'F_GET_TOKEN_FAILED': // 无法获取Token，可能是CAS认证失败，由Token中间件输出
    case 'F_REFRESH_TOKEN_FAILED': // 无法刷新Token，可能是CAS认证失败，由Token中间件输出
      // eslint-disable-next-line no-console
      console.log(new Date().toISOString(), `Login required (code: ${error.code})`);
      // CAS认证由这里发起
      // eslint-disable-next-line no-underscore-dangle
      cas._login(req, res, next);
      break;

    default:
      // eslint-disable-next-line no-console
      console.error(new Date().toISOString(), 'errorHandler', error);
      res.status(500).send('Internal server error').end();
      break;
  }
});

app.on('error', (err) => {
  // eslint-disable-next-line no-console
  console.error(new Date().toISOString(), 'Server error: \n%s\n%s ', err.stack || '');
});

app.listen(expressPort, () => {
  // eslint-disable-next-line no-console
  console.log(`Express server listen in ${expressPort}`);
});
