<template>
  <Select
    class="pima-select"
  >
    <Option
      v-for="item in options"
      :key="item.value"
      :value="item.value"
      :label="item.label"
    />
  </Select>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';


export default defineComponent({
  name: 'SelectOptions',

  props: {
    options: {
      type: Array,
      default: undefined,
    },
  },

  emits: [
  ],

  setup() {
    return {
    };
  },
});
</script>
