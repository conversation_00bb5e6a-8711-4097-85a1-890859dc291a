import { PUBLIC_PATH } from '@/config/public-path';
import { CommonModelApi } from '@/api/common/common-model-api';


interface ITokenInfo {
  tokenType: string;
  accessToken: string;
  expiresIn: string;
  refreshToken: string;
}

export class TokenInfo<PERSON>pi extends CommonModelApi<ITokenInfo> {
  constructor() {
    super({ baseURL: PUBLIC_PATH });
  }

  url() {
    return '/x-token-info';
  }
}
