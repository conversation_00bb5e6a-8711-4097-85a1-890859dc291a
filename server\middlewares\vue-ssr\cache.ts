let cache = null;

module.exports = () => {
  const { LRUCache } = require('lru-cache');

  const isProd = process.env.NODE_ENV === 'production';

  if (!cache) {
    cache = new LRUCache({
      max: 1000, // 缓存数量
      ttl: 1000 * 60 * 5, // 缓存条目在 300 秒后过期
    });
  }

  function isCacheable() {
    return isProd;
  }

  // 缓存是以页面地址为维度，需根据需求定缓存规则
  function genCacheKey(req, res) {
    const parts = [
      req.url,
      res.locals.appLoggedIn,
      res.locals.userCategory,
      res.locals.appLocale,
    ];
    return parts.join('|');
  }

  function getCachedItem(key) {
    return cache.get(key);
  }

  return {
    isCacheable,
    genCacheKey,
    getCachedItem,
  };
};
