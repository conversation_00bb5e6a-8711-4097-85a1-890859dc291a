import { parseISO } from 'date-fns';
import { TokenInfoApi } from '@/api/token-info';


export type Token = {
  tokenType?: string;
  accessToken?: string;
  expiresIn?: Date;
  refreshToken?: string;
};

class TokenService {
  async getToken(): Promise<Token> {
    const api = new TokenInfoApi();
    const res = await api.send();

    const token = {
      tokenType: res.model.tokenType,
      accessToken: res.model.accessToken,
      expiresIn: parseISO(res.model.expiresIn),
      refreshToken: res.model.refreshToken,
    };

    return token;
  }
}

export const tokenService = new TokenService();
