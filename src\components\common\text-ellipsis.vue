<template>
  <div class="text-ellipsis">
    <div
      ref="rawContentRef"
      class="raw-content"
      @click.stop.prevent="() => {}"
    >
      <slot />
    </div>

    <Tooltip
      v-if="isOverflow"
      placement="bottom"
      theme="light"
      transfer-class-name="tooltip-popper-text-ellipsis"
      transfer
    >
      <div
        class="shrinked"
        :style="computedStyle"
      >
        <slot />
      </div>
      <template #content>
        <slot />
      </template>
    </Tooltip>
    <div v-else>
      <slot />
    </div>
  </div>
</template>


<script lang='ts'>
import { defineComponent, ref, computed, onMounted, onUpdated, onBeforeUnmount, nextTick } from 'vue';
import _ from 'lodash';


export default defineComponent({
  name: 'TextEllipsis',

  components: {
  },

  props: {
    value: {
      type: String,
      default: '',
    },
    maxLines: {
      type: Number,
      default: 2,
    },
    lineHeight: {
      type: Number,
      default: 21,
    },
  },

  setup(props) {
    const rawContentRef = ref();
    const isOverflow = ref(false);


    const computedStyle = computed(() => {
      if (isOverflow.value) {
        return {
          height: `${props.maxLines * props.lineHeight}px`,
          lineClamp: `${props.maxLines}`,
        };
      }

      return {};
    });

    function calcOverflow() {
      const b = (rawContentRef.value.offsetHeight > props.maxLines * props.lineHeight);
      if (b !== isOverflow.value) {
        isOverflow.value = b;
      }
    }

    const onResize = _.debounce(calcOverflow, 100);

    onMounted(async () => {
      await nextTick();
      calcOverflow();
      window.addEventListener('resize', onResize);
    });

    onUpdated(() => {
      calcOverflow();
    });

    onBeforeUnmount(() => {
      window.removeEventListener('resize', onResize);
    });


    return {
      rawContentRef,
      isOverflow,
      computedStyle,
    };
  },
});
</script>


<style lang="less">
.tooltip-popper-text-ellipsis {
  .ivu-tooltip-inner {
    overflow: hidden;
    white-space: normal;
    word-break: break-word;
  }
}
</style>

<style lang="less" scoped>
.text-ellipsis {
  position: relative;
  overflow: hidden;

  .raw-content {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    visibility: hidden;
  }

  .shrinked {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    word-break: break-word;
  }

  :deep(.ivu-tooltip-rel) {
    vertical-align: middle;
  }
}
</style>
