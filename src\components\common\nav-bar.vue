<template>
  <div class="pima-nav-bar">
    <div
      v-if="navTitle"
      class="nav-title"
    >
      {{ navTitle }}
    </div>

    <div class="nav-item-wrap">
      <div
        v-for="(item, index) in navItemList"
        :key="index"
        class="nav-item"
        :class="navItemClass(item)"
        @click="scrollIntoView(item)"
      >
        <span class="dot" />
        {{ item.title }}
      </div>
    </div>

    <slot name="footer" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, toRefs, watch, watchEffect } from 'vue';

defineOptions({
  name: 'NavBar',
});

interface Props {
  navTitle?: string;
  navItemList?: Array<NavItem>;
  // 当前激活的导航项唯一标识
  activeItemKey?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  navTitle: '',
  navItemList: () => [],
  activeItemKey: null,
});

const { navItemList, activeItemKey } = toRefs(props);
const currentItemKey = ref('');

function initActiveItem() {
  watchEffect(() => {
    if (navItemList.value.length > 0 && !currentItemKey.value) {
      currentItemKey.value = navItemList.value[0].key;
    }
  });
}

// 用于区分是点击滚动还是滑动
const flag = ref(0);

function scrollIntoView(item) {
  currentItemKey.value = item.key;
  flag.value = 1;
  item.scrollIntoView();
}

function navItemClass({ key }) {
  if (flag.value < 0) {
    return key === activeItemKey.value ? 'active' : '';
  }

  return key === currentItemKey.value ? 'active' : '';
}

watch(
  activeItemKey,
  () => {
    if (flag.value > 0) {
      flag.value = 0;
    } else {
      flag.value -= 1;
    }
  },
);

onMounted(() => {
  initActiveItem();
});
</script>


<style lang="less" scoped>
@paddingLeft: 24px;

.pima-nav-bar {
  position: fixed;
  top: 180px;
  right: 40px;
  z-index: 1;
  min-width: 120px;
  max-width: 200px;
  background: #fff;
  box-shadow: 0 5px 15px -5px fade(#000, 12%);

  .nav-title {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    color: #fff;
    font-size: 14px;
    background: #365aa4;
  }

  .nav-item-wrap {
    padding: 0 @paddingLeft;
    border: 1px solid #f0f0f0;

    .nav-item {
      position: relative;
      padding: 12px 0;
      color: #555;
      font-size: 14px;
      line-height: 20px;
      cursor: pointer;
      transition: all 0.2s ease-in-out;

      &:hover,
      &.active {
        color: var(--primary-color);
        font-weight: bold;
      }

      &.active {
        &::before {
          position: absolute;
          top: 0;
          left: -@paddingLeft;
          width: 4px;
          height: 100%;
          background: var(--primary-color);
          content: "";
        }
      }

      & + .nav-item {
        border-top: 1px solid #f5f5f5;
      }
    }
  }
}
</style>
