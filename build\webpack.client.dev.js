const path = require('path');
const { DefinePlugin } = require('webpack');
const { merge } = require("webpack-merge");
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const StylelintWebpackPlugin = require('stylelint-webpack-plugin');

const baseConfig =  require('./webpack.base');
const VueSSRClientPlugin = require('./plugins/vue-ssr-client-plugin');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = merge(baseConfig, {
  mode: 'development',
  target: ['web', 'es5'],
  entry: {
    app: resolve('../src/entry-client.ts'),
  },
  plugins: [
    new DefinePlugin({
      __VUE_OPTIONS_API__: 'true',
      __VUE_PROD_DEVTOOLS__: 'false',
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
    }),
    new ForkTsCheckerWebpackPlugin(),
    new StylelintWebpackPlugin({
      files: ['**/*.{vue,css,less}'],
    }),
    new VueSSRClientPlugin(),
  ],
});
