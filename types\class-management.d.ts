export interface SearchSimpleModelType {
  /**
     * 创建结束时间 yyyy-MM-dd
     */
  creEndTime?: string;
  /**
     * 创建开始时间 yyyy-MM-dd
     */
  creStartTime?: string;
  /**
     * 所属学院ID
     */
  facId?: string;
  /**
     * ids 选中的数据
     */
  ids?: number[];
  /**
     * 关键字
     */
  keyword?: string;
}


export interface ClassMgtListItemType {
  /**
     * 班级名称
     */
  clsName?: string;
  /**
     * 班级人数
     */
  clsPersons?: number;
  /**
     * 班级状态 数据字典：CLS_STATUS
     */
  clsStatus?: string;
  /**
     * 班导师ID
     */
  clsSupervisorIdMulti?: number[];
  /**
     * 班导师姓名
     */
  clsSupervisorNameMulti?: string[];
  /**
     * 班导师工号
     */
  clsSupervisorNoMulti?: string[];
  /**
     * 创建时间
     */
  creTime?: Date;
  /**
     * 是否可以删除 枚举：是：Y，否：N
     */
  deletableInd?: string;
  /**
     * 是否可以编辑 枚举：是：Y，否：N
     */
  editableInd?: string;
  /**
     * 学院ID
     */
  facId?: number;
  /**
     * 学院名称
     */
  facName?: string;
  /**
     * 是否可以设为毕业班 枚举：是：Y，否：N
     */
  gradableInd?: string;
  /**
     * 主键ID
     */
  id?: number;
  /**
     * 操作人
     */
  updName?: string;
  /**
     * 操作时间
     */
  updTime?: Date;
}

export type ClassMgtDetailType = ClassMgtListItemType;


export interface ClassEditDTO {
  facId: number,
  clsName: string,
  clsStatus: string
}
