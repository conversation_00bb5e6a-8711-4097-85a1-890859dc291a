<script setup lang="ts">
import { AttachmentVO } from '^/types/attachment';


interface Props {
  attachments: AttachmentVO[]
  size?:number
  multiple?:boolean
}

withDefaults(defineProps<Props>(), {
  attachments: () => [],
  size: 100,
  multiple: false,
});


</script>


<template>
  <template v-if="attachments && attachments.length > 0">
    <div
      v-if="!multiple"
      class="single-file"
    >
      <a
        :href="attachments[0].filePath.normal"
        target="_blank"
      >
        <img
          :src="attachments[0].filePath.small"
          :width="size"
        >
      </a>
    </div>

    <div
      v-else
      class="multiple-file"
    >
      <a
        v-for="attachment in attachments"
        :key="attachment.id"
        :href="attachment.filePath.normal"
        target="_blank"
      >
        <img
          :src="attachment.filePath.small"
          :width="size"
        >
      </a>
    </div>
  </template>
</template>


<style lang="less" scoped>
.multiple-file{
  display: flex;
  flex-wrap: wrap;
  gap:10px;
  align-items: center;

}

.multiple-file,
.single-file{
  img{
    height: auto;
  }
}
</style>
