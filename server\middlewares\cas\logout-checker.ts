/* eslint-disable @typescript-eslint/naming-convention */
module.exports = function createLogoutCheckerMiddleware(options) {
  const { serviceUrl } = options;

  if (!serviceUrl) {
    throw new Error('[createLogoutCheckerMiddleware] serviceUrl config option required');
  }

  return function logoutCheckerMiddleware(req, res, next) {
    if (req.query.service && req.query.service.indexOf(serviceUrl) !== 0) {
      res.sendStatus(406);
      res.end();
      return;
    }

    next();
  };
};
