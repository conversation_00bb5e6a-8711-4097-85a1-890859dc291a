import _ from 'lodash';


export interface PaginationParamsOption extends Record<string, unknown> {
  page: {
    page: number,
    limit: number,
  },
  [key: string]: unknown,
}

export interface PaginationResult extends Record<string, unknown> {
  page: number,
  limit: number,
  [key: string]: unknown,
}

export function paginationParams(params: PaginationParamsOption): PaginationResult {
  return {
    ..._.cloneDeep(_.omit(params, ['page'])),
    page: _.get(params, 'page.page', 1),
    limit: _.get(params, 'page.limit', 15),
  } as PaginationResult;
}
