import _ from 'lodash';


export function interceptorNullFilter(config) {
  const paramsWithoutNullKeys = Object.keys(config.params).filter((key) => config.params[key] !== null);
  const params = _.pick(config.params, paramsWithoutNullKeys);
  Object.assign(config, {
    params,
  });

  if (_.isPlainObject(config.data)) {
    const dataWithoutNullKeys = Object.keys(config.data).filter((key) => config.data[key] !== null);
    const data = _.pick(config.data, dataWithoutNullKeys);
    Object.assign(config, {
      data,
    });
  }

  return config;
}
