<script lang="ts" setup>
withDefaults(defineProps<{
  fileUrl: string,
}>(), {
  fileUrl: '',
});

const emit = defineEmits([
  'on-delete',
]);


function onDel() {
  emit('on-delete');
}
</script>


<template>
  <div class="have-uploaded">
    <img
      v-if="fileUrl"
      class="preview"
      :src="fileUrl"
    >
    <Icon
      class="btn-delete"
      type="md-close-circle"
      @click="onDel()"
    />
  </div>
</template>


<style lang="less" scoped>
.have-uploaded {
  position: relative;
  display: inline-flex;
  margin-top: 20px;
  margin-right: 30px;

  .btn-delete {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 30px;
    background-color: #fff;
    border-radius: 50%;
    cursor: pointer;
  }

  .preview {
    width: 200px;
    height: 112px;
    object-fit: contain;
  }
}
</style>
