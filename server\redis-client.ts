module.exports = function createRedisClient(options) {
  const IORedis = require('ioredis');

  const { redisKeyPrefix: keyPrefix } = options;

  if (!keyPrefix) {
    throw new Error('[redis-client] redisKeyPrefix not configured');
  }

  let redisClient;
  if (process.env.REDIS_CLUSTER === 'on') {
    let nodes = [];
    if (process.env.REDIS_URL) {
      const urls = process.env.REDIS_URL.split(',');
      nodes = urls.map((url_) => url_.trim());
    }

    const redisClientOptions = {};
    if (process.env.REDIS_PASSWORD) {
      Object.assign(redisClientOptions, {
        redisOptions: {
          password: process.env.REDIS_PASSWORD,
        },
      });
    }

    if (process.env.REDIS_NAT_MAP) {
      const natMap = {};
      let rawNatMapParis = process.env.REDIS_NAT_MAP.split(',');
      rawNatMapParis = rawNatMapParis.map((rawNatMapPari) => rawNatMapPari.trim());
      const reg = /^(\d{1,3}(\.\d{1,3}){3}:\d+)=>((\w|.)+)?:(\d+)$/;
      rawNatMapParis.forEach((rawNatMapPari) => {
        const matchs = reg.exec(rawNatMapPari);
        if (matchs && matchs.length === 6) {
          const from = matchs[1];
          const host = matchs[3];
          const port = matchs[5];
          natMap[from] = { host, port };
        }
      });
      if (Object.keys(natMap).length > 0) {
        Object.assign(redisClientOptions, { natMap });
      }
    }

    Object.assign(redisClientOptions, { keyPrefix });
    redisClient = new IORedis.Cluster(nodes, redisClientOptions);
  } else {
    const redisClientOptions = { keyPrefix };
    redisClient = new IORedis(process.env.REDIS_URL, redisClientOptions);
  }

  redisClient.on('ready', () => {
  // eslint-disable-next-line no-console
    console.log('Redis is ready');
  });

  redisClient.on('error', (err) => {
    // eslint-disable-next-line no-console
    console.error('Redis client error', err);
  });

  return redisClient;
};
