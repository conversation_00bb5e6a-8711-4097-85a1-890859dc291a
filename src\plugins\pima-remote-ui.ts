import type { App } from 'vue';
import type { I18n } from 'vue-i18n';
import { SUPPORT_LOCALES } from '@/config/locale';
import {
  LOCAL_BDC_CORE_API_BASE_URL,
  LOCAL_BDC_ARCH_API_BASE_URL,
  LOCAL_BDC_IMPORT_API_BASE_URL,
  LOCAL_BDC_EXPORT_API_BASE_URL } from '@/config/api';


export const PimaRemoteUIPlugin = {
  install(app: App, options: {
    i18n: I18n
  }) {
    if (!options.i18n) {
      throw new Error('PimaRemoteUIPlugin need i18n option');
    }

    if (typeof document !== 'undefined') {
      import('pimaRemoteUI/init').then(({ init }) => {
        init({
          apiBaseUrls: {
            bdcCoreApiBaseUrl: LOCAL_BDC_CORE_API_BASE_URL,
            bdcArchApiBaseUrl: LOCAL_BDC_ARCH_API_BASE_URL,
            bdcImportApiBaseUrl: LOCAL_BDC_IMPORT_API_BASE_URL,
            bdcExportApiBaseUrl: LOCAL_BDC_EXPORT_API_BASE_URL,
          },
          // @ts-expect-error:  Property 'value' does not exist on type 'string'
          locale: options.i18n.global.locale.value,
          serviceCode: process.env.SERVICE_CODE,
          appHeader: {
            supportLocales: SUPPORT_LOCALES,
          },
        }, true);
      });
    }
  },
};
