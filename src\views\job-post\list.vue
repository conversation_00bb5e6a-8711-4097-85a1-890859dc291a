<script setup lang="ts">
import { reactive, onActivated } from 'vue';
import { useRouter } from 'vue-router';

import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import PimaImportExportCenter from '@/components/common/import-export-center.vue';
import WarningModal from '@/components/biz/modal/warning-modal.vue';
import SearchSimple from './components/search-simple.vue';
import QueryTable from './components/query-table.vue';

import { ExportApi } from '@/api/job-post/export';
import { ListApi } from '@/api/job-post/list';
import { useQueryTable } from '@/uses/query-table';
import { useTableLoader } from '@/uses/table-loader';
import { useImportExport } from '@/uses/import-export';

import { handleListParams } from './helps/handle-api-data';
import { createDeleteModel, createSearchSimpleModel } from './helps/models';
import { push } from '@/helps/navigation';
import { RouterName as RN } from '@/config/router';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { DeleteApi } from '@/api/job-post/delete';
import { namespaceT } from '@/helps/namespace-t';


defineOptions({
  name: 'JobPostList',
});

const router = useRouter();
const tc = namespaceT('common');

const deleteModel = reactive(createDeleteModel());

const loadData = useTableLoader(ListApi, handleListParams);
const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSearchSimpleModel(),
});


const onExport = async () => {
  const api = new ExportApi<{ model: number }>();
  api.params = handleListParams(qt.query.value);
  const data = await api.send();
  return { taskId: data.model };
};

const importExportCenter = useImportExport({
  exportData: onExport,
});

const onAdd = () => {
  push(router, {
    name: RN.JobPostAdd,
  });
};

const onEdit = (id:number) => {
  push(router, {
    name: RN.JobPostEdit,
    params: {
      id,
    },
  });
};

const onReapply = (id:number) => {
  push(router, {
    name: RN.JobPostReapply,
    params: {
      id,
    },
  });
};

const onView = (id:number) => {
  push(router, {
    name: RN.JobPostDetail,
    params: {
      id,
    },
  });
};

const onDelete = async () => {
  try {
    deleteModel.loading = true;
    const api = new DeleteApi({ id: deleteModel.id });
    await api.send();
    openToastSuccess(tc('hint.successfullyDeleted'));

    deleteModel.visible = false;
    qt.search();
  } catch (error) {
    openToastError(error.message);
  } finally {
    deleteModel.loading = false;
  }
};

const showDeleteModal = (id:number) => {
  deleteModel.id = id;
  deleteModel.visible = true;
};


onActivated(() => {
  qt.load();
});

</script>

<template>
  <SearchSimple
    v-model="qt.simpleSearchModel"
    :exporting="importExportCenter.exporting"
    @on-export="importExportCenter.exportData"
    @on-add="onAdd"
    @on-search="qt.search"
  />

  <TableScroll>
    <QueryTable
      :data="qt.table.data"
      :loading="qt.table.loading"
      @on-view="onView"
      @on-edit="onEdit"
      @on-reapply="onReapply"
      @on-delete="showDeleteModal"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>

  <WarningModal
    v-model="deleteModel.visible"
    :loading="deleteModel.loading"
    :title="deleteModel.title"
    :content="deleteModel.content"
    @on-confirm="onDelete"
  />


  <PimaImportExportCenter hide-import-task />
</template>
