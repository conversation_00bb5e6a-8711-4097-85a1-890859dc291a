import { defineComponent, useSlots, h, ref, onBeforeMount } from 'vue';


export default defineComponent({
  name: 'ClientOnly',

  props: {
    tag: {
      type: String,
      default: 'div',
    },
  },

  setup(props) {
    const show = ref(false);
    const slots = useSlots();

    onBeforeMount(() => {
      show.value = true;
    });

    return () => (show.value ? h(props.tag, slots.default && slots.default()) : null);
  },
});
