import { RequestParams } from '@/api/common/common-api';
import { CommonModelApi } from '@/api/common/common-model-api';
import { LOCAL_BDC_CLIENT_API_BASE_URL } from '@/config/api';
import { textByLocale } from '@/helps/locale';

interface RegionsItem {
  id: number,
  parentId: number,
  code: number,
  name: string,
  enName: string,
  hasChild: boolean,
  firstCn: string,
  firstEn: string
}

export interface CascaderRegionsItem extends RegionsItem {
  value: number,
  label: string,
  children?: CascaderRegionsItem[],
  hasChild:boolean,
  loading?: boolean,
}


export class RegionsApi extends CommonModelApi<RegionsItem[]> {
  constructor() {
    super({
      baseURL: LOCAL_BDC_CLIENT_API_BASE_URL,
    });
  }

  url() {
    return '/regions/tree';
  }

  defaultParams(): RequestParams {
    return {
      /** 默认传6892 获取中国数据 */
      parentId: 6892,
    };
  }

  async sendForRegions(): Promise<CascaderRegionsItem[]> {
    const res = await super.send();
    const list = (res.model || []).map((item) => {
      const obj = {
        ...item,
        label: textByLocale(item.name, item.enName, true),
        value: item.id,
      };

      if (item.hasChild) {
        Object.assign(obj, {
          loading: false,
          children: [],
        });
      }

      return obj;
    });

    return list;
  }
}
