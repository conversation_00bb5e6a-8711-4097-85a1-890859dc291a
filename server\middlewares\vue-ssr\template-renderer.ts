/* eslint-disable */
// @ts-nocheck
const serialize = require('serialize-javascript');
const { template: compile } = require('lodash');

const { isJS, isCSS } = require('../util');


type ClientManifest = {
  publicPath: string
  all: Array<string>
  initial: Array<string>
  async: Array<string>
  modules: {
    [id: string]: Array<number>
  }
  hasNoCssVersion?: {
    [file: string]: boolean
  }
};

type TemplateRendererOptions = {
  template?:
  | string
  | ((content: string, context: any) => string | Promise<string>)
  inject?: boolean
  clientManifest?: ClientManifest
  shouldPreload?: (file: string, type: string) => boolean
  shouldPrefetch?: (file: string, type: string) => boolean
  serializer?: Function
};

type ParsedTemplate = {
  head: (data: any) => string
  neck: (data: any) => string
  tail: (data: any) => string
};

type Resource = {
  file: string
  extension: string
  fileWithoutQuery: string
  asType: string
};

type AsyncFileMapper = (files: Array<string>) => Array<string>;


function getPreloadType(ext) {
  if (ext === 'js') {
    return 'script';
  }
  if (ext === 'css') {
    return 'style';
  }
  if (/jpe?g|png|svg|gif|webp|ico/.test(ext)) {
    return 'image';
  }
  if (/woff2?|ttf|otf|eot/.test(ext)) {
    return 'font';
  }

  // not exhausting all possibilities here, but above covers common cases
  return '';
}

function normalizeFile(file) {
  const path = require('path');
  const withoutQuery = file.replace(/\?.*/, '');
  const extension = path.extname(withoutQuery).slice(1);
  return {
    file,
    extension,
    fileWithoutQuery: withoutQuery,
    asType: getPreloadType(extension),
  };
}

function parseTemplate(
  template: string,
  contentPlaceholder: string = '<!--vue-ssr-outlet-->',
): ParsedTemplate {
  const compileOptions = {
    escape: /{{([^{][\s\S]+?[^}])}}/g,
    interpolate: /{{{([\s\S]+?)}}}/g,
  };

  if (typeof template === 'object') {
    return template;
  }

  let i = template.indexOf('</head>');
  const j = template.indexOf(contentPlaceholder);

  if (j < 0) {
    throw new Error('Content placeholder not found in template.');
  }

  if (i < 0) {
    i = template.indexOf('<body>');
    if (i < 0) {
      i = j;
    }
  }

  return {
    head: compile(template.slice(0, i), compileOptions),
    neck: compile(template.slice(i, j), compileOptions),
    tail: compile(template.slice(j + contentPlaceholder.length), compileOptions),
  };
}

function mapIdToFile(id, clientManifest) {
  const files: string[] = [];
  const fileIndices = clientManifest.modules[id];
  if (fileIndices) {
    fileIndices.forEach((index) => {
      const file = clientManifest.all[index];
      // only include async files or non-js, non-css assets
      if (
        file
        && (clientManifest.async.indexOf(file) > -1
          || !/\.(js|css)($|\?)/.test(file))
      ) {
        files.push(file);
      }
    });
  }
  return files;
}

function createMap(clientManifest) {
  const map = new Map();
  Object.keys(clientManifest.modules).forEach((id) => {
    map.set(id, mapIdToFile(id, clientManifest));
  });
  return map;
}

function createMapper(clientManifest: ClientManifest): AsyncFileMapper {
  const map = createMap(clientManifest);
  // map server-side moduleIds to client-side files
  return function mapper(moduleIds: Array<string>): Array<string> {
    const res = new Set<string>();
    for (let i = 0; i < moduleIds.length; i++) {
      const mapped = map.get(moduleIds[i]);
      if (mapped) {
        for (let j = 0; j < mapped.length; j++) {
          res.add(mapped[j]);
        }
      }
    }
    return Array.from(res);
  };
}

const __DEV__ = process.env.NODE_ENV !== 'production';


module.exports = class TemplateRenderer {
  options: TemplateRendererOptions;

  inject: boolean;

  parsedTemplate: ParsedTemplate | Function | null;

  clientManifest: ClientManifest;

  publicPath: string;

  preloadFiles: Array<Resource>;

  prefetchFiles: Array<Resource>;

  mapFiles: AsyncFileMapper;

  serialize: Function;

  constructor(options: TemplateRendererOptions) {
    this.options = options;
    this.inject = options.inject !== false;
    // if no template option is provided, the renderer is created
    // as a utility object for rendering assets like preload links and scripts.

    const { template } = options;
    this.parsedTemplate = template
      ? typeof template === 'string'
        ? parseTemplate(template)
        : template
      : null;

    // function used to serialize initial state JSON
    this.serialize = options.serializer
      || ((state) => {
        return serialize(state, { isJSON: true });
      });

    // extra functionality with client manifest
    if (options.clientManifest) {
      const clientManifest = (this.clientManifest = options.clientManifest);
      // ensure publicPath ends with /
      this.publicPath = clientManifest.publicPath === ''
        ? ''
        : clientManifest.publicPath.replace(/([^\/])$/, '$1/');
      // preload/prefetch directives
      this.preloadFiles = (clientManifest.initial || []).map(normalizeFile);
      this.prefetchFiles = (clientManifest.async || []).map(normalizeFile);
      // initial async chunk mapping
      this.mapFiles = createMapper(clientManifest);
    }
  }

  bindRenderFns(context: Record<string, any>) {
    const renderer: any = this;
    ['ResourceHints', 'State', 'Scripts', 'Styles'].forEach((type) => {
      context[`render${type}`] = renderer[`render${type}`].bind(
        renderer,
        context,
      );
    });
    // also expose getPreloadFiles, useful for HTTP/2 push
    context.getPreloadFiles = renderer.getPreloadFiles.bind(renderer, context);
  }

  // render synchronously given rendered app content and render context
  render(
    content: string,
    context: Record<string, any> | null,
  ): string | Promise<string> {
    const template = this.parsedTemplate;
    if (!template) {
      throw new Error('render cannot be called without a template.');
    }
    context = context || {};

    if (typeof template === 'function') {
      return template(content, context);
    }

    if (this.inject) {
      return (
        template.head(context)
        + (context.head || '')
        + this.renderResourceHints(context)
        + this.renderStyles(context)
        + template.neck(context)
        + content
        + this.renderState(context)
        + this.renderScripts(context)
        + template.tail(context)
      );
    }
    return (
      template.head(context)
        + template.neck(context)
        + content
        + template.tail(context)
    );
  }

  renderStyles(context: Record<string, any>): string {
    const initial = this.preloadFiles || [];
    const async = this.getUsedAsyncFiles(context) || [];
    const cssFiles = initial.concat(async).filter(({ file }) => isCSS(file));
    return (
      // render links for css files
      (cssFiles.length
        ? cssFiles
          .map(
            ({ file }) => `<link rel="stylesheet" href="${this.publicPath}${file}">`,
          )
          .join('')
        : '')
      // context.styles is a getter exposed by vue-style-loader which contains
      // the inline component styles collected during SSR
      + (context.styles || '')
    );
  }

  renderResourceHints(context: Object): string {
    return this.renderPreloadLinks(context) + this.renderPrefetchLinks(context);
  }

  getPreloadFiles(context: Object): Array<Resource> {
    const usedAsyncFiles = this.getUsedAsyncFiles(context);
    if (this.preloadFiles || usedAsyncFiles) {
      return (this.preloadFiles || []).concat(usedAsyncFiles || []);
    }
    return [];
  }

  renderPreloadLinks(context: Object): string {
    const files = this.getPreloadFiles(context);
    const { shouldPreload } = this.options;
    if (files.length) {
      return files
        .map(({ file, extension, fileWithoutQuery, asType }) => {
          let extra = '';
          // by default, we only preload scripts or css
          if (!shouldPreload && asType !== 'script' && asType !== 'style') {
            return '';
          }
          // user wants to explicitly control what to preload
          if (shouldPreload && !shouldPreload(fileWithoutQuery, asType)) {
            return '';
          }
          if (asType === 'font') {
            extra = ` type="font/${extension}" crossorigin`;
          }
          return `<link rel="preload" href="${this.publicPath}${file}"${
            asType !== '' ? ` as="${asType}"` : ''
          }${extra}>`;
        })
        .join('');
    }
    return '';
  }

  renderPrefetchLinks(context: Object): string {
    const { shouldPrefetch } = this.options;
    if (this.prefetchFiles) {
      const usedAsyncFiles = this.getUsedAsyncFiles(context);
      const alreadyRendered = (file) => {
        return usedAsyncFiles && usedAsyncFiles.some((f) => f.file === file);
      };
      return this.prefetchFiles
        .map(({ file, fileWithoutQuery, asType }) => {
          if (shouldPrefetch && !shouldPrefetch(fileWithoutQuery, asType)) {
            return '';
          }
          if (alreadyRendered(file)) {
            return '';
          }
          return `<link rel="prefetch" href="${this.publicPath}${file}">`;
        })
        .join('');
    }
    return '';
  }

  renderState(
    context: Record<string, any>,
    options?: Record<string, any>,
  ): string {
    const { contextKey = 'state', windowKey = '__INITIAL_STATE__' } = options || {};
    const state = this.serialize(context[contextKey]);
    const autoRemove = __DEV__
      ? ''
      : ';(function(){var s;(s=document.currentScript||document.scripts[document.scripts.length-1]).parentNode.removeChild(s);}());';
    const nonceAttr = context.nonce ? ` nonce="${context.nonce}"` : '';
    return context[contextKey]
      ? `<script${nonceAttr}>window.${windowKey}=${state}${autoRemove}</script>`
      : '';
  }

  renderScripts(context: Object): string {
    if (this.clientManifest) {
      const initial = this.preloadFiles.filter(({ file }) => isJS(file));
      const async = (this.getUsedAsyncFiles(context) || []).filter(({ file }) => isJS(file));
      const needed = [initial[0]].concat(async, initial.slice(1));
      return needed
        .map(({ file }) => {
          return `<script src="${this.publicPath}${file}" defer></script>`;
        })
        .join('');
    }
    return '';
  }

  getUsedAsyncFiles(context: Record<string, any>): Array<Resource> | undefined {
    if (
      !context._mappedFiles
      && context._registeredComponents
      && this.mapFiles
    ) {
      const registered: any[] = Array.from(context._registeredComponents);
      context._mappedFiles = this.mapFiles(registered).map(normalizeFile);
    }
    return context._mappedFiles;
  }

  // create a transform stream
  // createStream(context: Record<string, any> | undefined): TemplateStream {
  //   if (!this.parsedTemplate) {
  //     throw new Error('createStream cannot be called without a template.')
  //   }
  //   //@ts-expect-error
  //   return new TemplateStream(this, this.parsedTemplate, context || {})
  // }
};


// import { Transform } from 'stream'

// export default class TemplateStream extends Transform {
//   started: boolean
//   renderer: TemplateRenderer
//   template: ParsedTemplate
//   context: Record<string, any>
//   inject: boolean

//   constructor(
//     renderer: TemplateRenderer,
//     template: ParsedTemplate,
//     context: Record<string, any>
//   ) {
//     super()
//     this.started = false
//     this.renderer = renderer
//     this.template = template
//     this.context = context || {}
//     this.inject = renderer.inject
//   }

//   _transform(data: Buffer | string, encoding: string, done: Function) {
//     if (!this.started) {
//       this.emit('beforeStart')
//       this.start()
//     }
//     this.push(data)
//     done()
//   }

//   start() {
//     this.started = true
//     this.push(this.template.head(this.context))

//     if (this.inject) {
//       // inline server-rendered head meta information
//       if (this.context.head) {
//         this.push(this.context.head)
//       }

//       // inline preload/prefetch directives for initial/async chunks
//       const links = this.renderer.renderResourceHints(this.context)
//       if (links) {
//         this.push(links)
//       }

//       // CSS files and inline server-rendered CSS collected by vue-style-loader
//       const styles = this.renderer.renderStyles(this.context)
//       if (styles) {
//         this.push(styles)
//       }
//     }

//     this.push(this.template.neck(this.context))
//   }

//   _flush(done: Function) {
//     this.emit('beforeEnd')

//     if (this.inject) {
//       // inline initial store state
//       const state = this.renderer.renderState(this.context)
//       if (state) {
//         this.push(state)
//       }

//       // embed scripts needed
//       const scripts = this.renderer.renderScripts(this.context)
//       if (scripts) {
//         this.push(scripts)
//       }
//     }

//     this.push(this.template.tail(this.context))
//     done()
//   }
// }
