<script setup lang="ts">
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { onBeforeMount } from 'vue';


const store = usePostTypeStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
