import type { Composer } from 'vue-i18n';

import { i18n } from '@/i18n';
import { Locale, LocaleType } from '@/config/locale';


/** @function
 * 当前系统语言
 * @returns 系统语言字符串
 */
export function getLocale() {
  return (i18n.global as unknown as Composer).locale.value as LocaleType;
}

/**
 * 根据当前系统语言获取对应值的类型定义。
 *
 * @param {(Function|string)} chnFn - 繁体中文值，可以是返回字符串的函数或直接的字符串值。
 * @param {(Function|string)} engFn - 英文值，可以是返回字符串的函数或直接的字符串值。
 * @returns {string} 根据系统语言（繁体中文或英文）返回的对应值。
 */
export function valueByLocale(chnFn: (() => string) | string, engFn: (() => string) | string): string {
  if (getLocale() === Locale.ZH_CN) {
    return typeof chnFn === 'function' ? chnFn() : chnFn;
  }

  return typeof engFn === 'function' ? engFn() : engFn;
}

/** @function
 * 根据当前系统语言获取对应文本
 * @param {string} chnText - 繁体中文文本
 * @param {string} engText - 英文文本
 * @param {boolean} [fallback=false] - 当前语言为空白时fallback文本
 * @returns 系统语言对应文本
 */
export function textByLocale(chnText: string, engText: string, fallback: boolean = false): string {
  if (fallback) {
    return valueByLocale(() => (chnText || engText), () => (engText || chnText));
  }

  return valueByLocale(chnText, engText);
}
