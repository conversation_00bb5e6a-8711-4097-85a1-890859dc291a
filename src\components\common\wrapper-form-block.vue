<template>
  <div class="pima-form-block-wrapper">
    <div
      v-if="title"
      class="pima-form-block-title-bar flex-space-between"
    >
      <div class="pima-form-block-title">
        {{ title }}
      </div>

      <slot name="right" />
    </div>

    <div class="pima-form-block-content">
      <slot />
    </div>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';


export default defineComponent({
  name: 'WrapperFormBlock',

  props: {
    title: {
      type: String,
      default: null,
    },
  },
});
</script>

<style lang="less" scoped>
.flex-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
