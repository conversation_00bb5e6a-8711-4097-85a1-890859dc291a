<script lang='ts' setup>
import { defineAsyncComponent } from 'vue';

import ClientOnly from '@/components/common/client-only';

import emitter from '@/utils/event-bus';


const emits = defineEmits<{
  'on-logout': [];
}>();

// eslint-disable-next-line import/no-unresolved
const PimaAppHeader = defineAsyncComponent(() => import('pimaRemoteUI/PimaAppHeader'));

/** 桌面-服务编码 */
const dashboardCode = process.env.DASHBOARD_SERVICE_CODE;
function onClickServiceName() {
  emitter.emit('clickServiceName');
}

function onLogout() {
  emits('on-logout');
}

function onUserDataFetched(data) {
  emitter.emit('userDataFetched', data);
}
</script>


<template>
  <ClientOnly>
    <PimaAppHeader
      :dashboard-code="dashboardCode"
      @click-service-name="onClickServiceName()"
      @logout="onLogout()"
      @user-data-fetched="onUserDataFetched"
    />
  </ClientOnly>
</template>
