import { reactive, type App } from 'vue';
import { Auth } from '@/config/auth';


export const AuthPlugin = {
  install(app: App) {
    const ob = reactive({
      auths: null,
    });

    Object.assign(app.config.globalProperties, {
      $setAuths: (auths: string[]) => {
        if (Array.isArray(auths)) {
          ob.auths = auths;
        }
      },

      $hasAuths: () => {
        return ob.auths !== null;
      },

      $can: (fn: (auth: typeof Auth) => boolean) => {
        const p = fn(Auth);
        return new Set(ob.auths).has(p);
      },
    });
  },
};
