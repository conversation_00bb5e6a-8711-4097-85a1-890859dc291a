export function scrollIntoView(viewRef) {
  viewRef.$el.scrollIntoView({
    behavior: 'smooth',
  });
}

export function scrollIntoFormErrorField(formRef) {
  const errorFields = formRef.fields?.filter((field) => field.validateState === 'error');
  const firstErrorField = [...errorFields].shift();
  if (firstErrorField) {
    firstErrorField.$el.scrollIntoView({
      behavior: 'smooth',
    });
  }
}
