<script setup lang="ts">
import { namespaceT } from '@/helps/namespace-t';
import { computed } from 'vue';
import NumberInput from './number-input.vue';

interface Props {
  step?:number;
  precision?:number;
  startMin?:number;
  startMax?:number;
  endMin?:number;
  endMax?:number;
  minDisable?:boolean;
  maxDisable?:boolean;
  startPlaceholder?:string;
  endPlaceholder?:string;
}

const props = withDefaults(defineProps<Props>(), {
  step: 1,
  precision: 0,
  startMin: 0,
  startMax: 9999,
  endMin: 0,
  endMax: 9999,
  minDisable: false,
  maxDisable: false,
  startPlaceholder: null,
  endPlaceholder: null,
});

const startNumber = defineModel<string>('startNumber');
const endNumber = defineModel<string>('endNumber');

const tc = namespaceT('common');

const endMin = computed(() => {
  if (!startNumber.value) {
    return props.endMin;
  }

  return props.endMin > +startNumber.value ? props.endMin : startNumber.value;
});

const startMax = computed(() => {
  if (!endNumber.value) {
    return props.startMax;
  }

  return props.startMax < +endNumber.value ? props.startMax : endNumber.value;
});


</script>


<template>
  <div class="number-input-range">
    <NumberInput
      v-model="startNumber"
      class="input-number"
      v-bind="$attrs"
      :step="step"
      :min="startMin"
      :max="startMax"
      :precision="precision"
      :disabled="minDisable"
      :placeholder="startPlaceholder || tc('placeholder.startNumber')"
    />

    <slot name="separator">
      <span class="separator">&ndash;</span>
    </slot>

    <NumberInput
      v-model="endNumber"
      class="input-number"
      v-bind="$attrs"
      :step="step"
      :min="endMin"
      :max="endMax"
      :precision="precision"
      :disabled="maxDisable"
      :placeholder="endPlaceholder || tc('placeholder.endNumber')"
    />
  </div>
</template>


<style lang="less" scoped>
.number-input-range {
  display: flex;
  align-items: center;
  min-width: 180px;

  .input-number{
    flex: 1;
    width: 140px;
  }

  .separator {
    flex-shrink: 0;
    margin: 0 10px;
  }
}
</style>
