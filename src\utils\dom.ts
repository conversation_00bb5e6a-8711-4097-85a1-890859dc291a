import _ from 'lodash';
import type { ComponentPublicInstance } from 'vue';

/**
 * 为元素绑定事件监听器，并提供移除该监听器的方法。
 *
 * @param el {HTMLElement} - 需要绑定事件的DOM元素。
 * @param event {string} - 事件名称，如 'click', 'mouseover' 等。
 * @param fn {EventListenerOrEventListenerObject} - 事件处理函数。
 * @param options {AddEventListenerOptions} - 可选的事件监听选项，如 capture, passive 等。
 * @returns - 返回一个函数，调用此函数可移除已添加的事件监听器。
 */
export function on(
  el: HTMLElement,
  event: string,
  fn: EventListenerOrEventListenerObject,
  options?: AddEventListenerOptions,
): () => void {
  el.addEventListener(event, fn, options);
  return () => el.removeEventListener(event, fn, options);
}

/**
 * 根据选择器查询文档中第一个匹配的元素。
 *
 * @param selector {string} - CSS选择器字符串。
 * @returns  第一个匹配的DOM元素，如果没有找到则返回null。
 */
export function e(selector: string): HTMLElement | null {
  return document.querySelector(selector);
}

/**
 * 在元素内部的最后位置插入HTML代码，并返回最后一个插入的子元素。
 *
 * @param element {HTMLElement} - 容器DOM元素。
 * @param html {string} - 要插入的HTML字符串。
 * @returns {HTMLElement} - 插入后位于最后的DOM元素。
 */
export function appendHTML(element: HTMLElement, html: string): HTMLElement {
  element.insertAdjacentHTML('beforeend', html);
  return element.lastChild as HTMLElement;
}

/**
 * 动态向<head>中添加CSS样式，并提供一个方法用于移除此样式。
 *
 * @param code {string} - CSS样式代码。
 * @returns {() => void} - 返回一个函数，调用此函数可从文档中移除添加的<style>元素。
 */
export function appendCss(code: string): () => void {
  const el = appendHTML(e('head'), `<style>${code}</style>`);
  return () => el.remove();
}

/**
 * 获取Vue实例元素的作用域ID（data-v-开头）。
 *
 * @param vueVm {Vue} - Vue实例。
 * @returns {string | undefined} - Vue组件的CSS作用域ID，如果找不到则返回undefined。
 */
export function vueCssScope(
  vueVm: ComponentPublicInstance,
): string | undefined {
  const attrIds = vueVm.$el.getAttributeNames();
  for (let i = 0; i < attrIds.length; i += 1) {
    const id = attrIds[i];
    if (id.startsWith('data-v-')) {
      return id;
    }
  }

  return undefined;
}


export function heightToTop(ele, targetEle = document.body) {
  const root = document.body;
  if (ele === root) {
    return 0;
  }

  let height = ele.offsetTop;
  let { offsetParent } = ele;
  while (![null, root, targetEle].includes(offsetParent)) {
    height += offsetParent.offsetTop;
    offsetParent = offsetParent.offsetParent;
  }
  return height;
}


export function findScrollParentElement(ele) {
  const root = document.body;
  if ([null, root].includes(ele)) {
    return ele;
  }

  const { offsetHeight, scrollHeight } = ele;
  if (scrollHeight > offsetHeight) {
    return ele;
  }

  return findScrollParentElement(ele.parentElement);
}

export function createScrollListener(onStop, duration = 100) {
  let scrollTop = 0;

  return _.debounce((event) => {
    const nowScrollTop = event.target.scrollTop;
    const res = {
      event,
      scrollTop: nowScrollTop,
      direction: nowScrollTop - scrollTop >= 0 ? 'bottom' : 'top',
    };

    scrollTop = nowScrollTop;
    if (typeof onStop === 'function') {
      onStop(res);
    }
  }, duration);
}
