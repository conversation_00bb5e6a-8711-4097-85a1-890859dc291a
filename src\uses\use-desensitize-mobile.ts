import { desensitizeMobile } from '@/helps/desensitize';
import { computed, ref } from 'vue';


export const useDesensitizeMobile = (mobileStr: string) => {
  const sensitiveMobile = ref(mobileStr);
  const insensitiveMobile = ref(desensitizeMobile(mobileStr));

  const isSensitive = ref(true);

  const onToggleStatus = () => {
    isSensitive.value = !isSensitive.value;
  };

  const mobile = computed(() => {
    return !isSensitive.value ? sensitiveMobile.value : insensitiveMobile.value;
  });


  return {
    mobile,
    isSensitive,

    onToggleStatus,
  };
};
