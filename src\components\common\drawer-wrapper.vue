<script setup lang="ts">
defineProps<{
  title?: string
}>();
const emits = defineEmits<{
  'on-closed': [],
}>();

const shown = defineModel<boolean>({ required: true });


function onClose() {
  shown.value = false;
  emits('on-closed');
}

</script>


<template>
  <div class="drawer-wrapper">
    <div
      class="left"
      :class="{ shown }"
    >
      <slot />
    </div>

    <div
      class="right"
      :class="{ shown }"
    >
      <div class="drawer">
        <div class="title-bar">
          <div class="title">
            {{ title }}
          </div>

          <Icon
            :size="28"
            type="md-close"
            class="icon-close"
            @click="onClose"
          />
        </div>

        <slot name="drawer-content" />
      </div>
    </div>
  </div>
</template>


<style scoped lang="less">
.drawer-wrapper {
  display: flex;
  width: 100%;
  height: 100%;

  > .left {
    flex: 1;
    max-width: calc(100vw - 240px);
    transition: all 0.3s ease-in-out;

    &.shown {
      max-width: calc(100vw - 240px - 630px);
    }
  }

  > .right {
    width: 0;
    height: 100%;
    overflow: hidden;
    border-left: 1px solid #efefef;
    transform: translateX(100%);
    transition: all 0.3s ease-in-out;

    &.shown {
      width: 630px;
      transform: translate(0);
    }

    .drawer {
      width: 100%;
      min-width: 500px;
      height: 100%;
      background-color: #fcfcfc;

      .title-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 64px;
        padding: 0 20px;
        background-color: #fff;
        border-bottom: 1px solid #efefef;

        .title {
          color: fade(#000, 85%);
          font-weight: 500;
          font-size: 16px;
        }

        .icon-close {
          cursor: pointer;
        }
      }

      .title-bar + .title-bar {
        border-top: 1px solid #efefef;
      }
    }
  }
}

:deep(.pima-drawer-form-wrapper) {
  position: relative;
  width: 100%;
  height: calc(100% - 64px - 50px);
  overflow: hidden;

  .pima-drawer-content-main {
    width: 100%;
    height: calc(100vh - 40px - 50px - 64px - 128px);
    overflow: hidden auto;
  }

  .pima-drawer-content-footer {
    position: absolute;
    right: 30px;
    bottom: 0;
    left: 30px;
  }
}
</style>
