import { LOCAL_BDC_IMPORT_API_BASE_URL, LOCAL_BDC_EXPORT_API_BASE_URL } from '@/config/api';


export const ImportExportCenterPlugin = {
  install(Vue, options = { i18n: null }) {
    if (!options.i18n) {
      throw new Error('ImportExportCenterPlugin need i18n option');
    }

    if (typeof window === 'object') {
      // eslint-disable-next-line import/no-unresolved
      import('pimaRemoteUI/PimaImportExportCenter').then(({ init }) => {
        init({
          locale: options.i18n.global.locale.value.value,
          import: {
            baseURL: LOCAL_BDC_IMPORT_API_BASE_URL,
          },
          export: {
            baseURL: LOCAL_BDC_EXPORT_API_BASE_URL,
          },
          serviceCode: process.env.SERVICE_CODE,
        });
      });
    }
  },
};


type ImportExportCenterCallback = (ixc) => void;


function createUseImportExportCenterPlugin() {
  let ixc = null;

  return function useImportExportCenterPluginSource(callback: undefined | ImportExportCenterCallback) {
    if (typeof window !== 'object') {
      return Promise.reject(new Error('PimaImportExportCenter can\'t run on the server side'));
    }

    return new Promise((resolve) => {
      function execute() {
        resolve(ixc);
        if (typeof callback === 'function') {
          callback(ixc);
        }
      }

      if (ixc) {
        execute();
        return;
      }

      // eslint-disable-next-line import/no-unresolved
      import('pimaRemoteUI/PimaImportExportCenter').then((importExportCenter) => {
        ixc = importExportCenter.default;
        execute();
      });
    });
  };
}


export const useImportExportCenterPlugin = createUseImportExportCenterPlugin();
