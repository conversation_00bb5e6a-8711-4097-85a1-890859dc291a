<template>
  <div class="operation-fail">
    <img
      class="icon"
      src="@/assets/img/operation-fail.png"
      alt
    >
    <slot v-if="$slots.default" />
    <template v-else>
      <slot
        v-if="$slots.title"
        name="title"
      />
      <div v-else>
        {{ $t('common.hint.savingFailed') }}
      </div>
      <div class="mt-10 error-message">
        <slot name="message" />
      </div>
      <i-button
        class="wm mt-30"
        type="primary"
        size="large"
        @click="$emit('go-back')"
      >
        {{ $t('common.action.back') }}
      </i-button>
    </template>
  </div>
</template>


<script>
export default {
  name: 'OperationFailed',

  emits: ['go-back'],
};
</script>


<style lang="less" scoped>
.operation-fail {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-top: 140px;
  color: #222;
  font-weight: 400;
  font-size: 24px;
  background: #fff;

  .icon {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }

  .error-message {
    color: #555;
    font-size: 16px;
  }
}
</style>
