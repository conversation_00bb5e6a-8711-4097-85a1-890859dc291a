<script lang="ts" setup>
import { namespaceT } from '@/helps/namespace-t';

interface DisabledDateFunction {
  (date: Date | string): boolean;
}

interface TimePickerOptionsType {
  disabledHours: Array<unknown>,
  disabledMinutes: Array<unknown>,
}

interface Props {
  type?: string;
  placeholder?: string | null;
  format?: string,
  disabledDate?: DisabledDateFunction,
  timePickerOptions?: TimePickerOptionsType,
}

withDefaults(defineProps<Props>(), {
  type: 'date',
  placeholder: null,
  format: namespaceT()('dateFormat.date'),
  disabledDate() {
    return () => false;
  },
  timePickerOptions() {
    return {
      disabledHours: [],
      disabledMinutes: [],
    };
  },
});

const emit = defineEmits([
  'input',
]);

function onInput(val) {
  emit('input', val);
}
</script>

<template>
  <DatePicker
    :type="type"
    :editable="false"
    :placeholder="placeholder"
    :format="format"
    :options="{ disabledDate }"
    :time-picker-options="timePickerOptions"
    class="pima-date-picker"
    v-bind="$attrs"
    @on-change="onInput"
  />
</template>
