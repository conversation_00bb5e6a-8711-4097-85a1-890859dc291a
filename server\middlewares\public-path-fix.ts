module.exports = function createPublicPathFixMiddleware(options) {
  const url = require('url');
  const { originPublicPath } = options;
  if (!originPublicPath) {
    throw new Error('[createPublicPathFixMiddleware] originPublicPath not configured');
  }

  const realPublicPath = originPublicPath.lastIndexOf('/') !== originPublicPath.length - 1
    ? `${originPublicPath}/`
    : originPublicPath;

  return function publicPathFixMiddleware(req, res, next) {
    if (req.xhr
      || originPublicPath === realPublicPath
      || url.parse(req.url).pathname.indexOf(realPublicPath) === 0) {
      next();
      return;
    }

    res.redirect(
      url.format({
        pathname: realPublicPath,
        query: req.query,
      }),
    );
  };
};
