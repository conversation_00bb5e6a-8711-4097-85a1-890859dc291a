<template>
  <CheckboxGroup
    class="pima-radio-group-vertical"
    v-bind="$attrs"
    v-on="$attrs"
  >
    <Checkbox
      v-for="item in options"
      :key="`${item.value}`"
      :label="item.value"
    >
      <span>{{ item.label }}</span>
    </Checkbox>
  </CheckboxGroup>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';


export default defineComponent({
  name: 'CheckboxGroupOptions',

  props: {
    options: {
      type: Array,
      default: undefined,
    },
  },

  emits: [
  ],

  setup() {
    return {
    };
  },
});
</script>
