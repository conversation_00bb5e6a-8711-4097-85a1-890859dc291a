/* eslint-disable @typescript-eslint/naming-convention */
module.exports = function createLoginStatusMiddleware(options) {
  const { casUserSessionKey, loggedInKey } = options;
  const config_casUserSessionKey = casUserSessionKey;
  const config_loggedInKey = loggedInKey || 'appLoggedIn';

  if (!casUserSessionKey) {
    throw new Error('casUserSessionKey config option required');
  }

  return function loginStatusMiddleware(req, res, next) {
    if (!req.xhr) {
      Object.assign(res.locals, {
        [config_loggedInKey]: !!req.session[config_casUserSessionKey],
      });
    }

    next();
  };
};
