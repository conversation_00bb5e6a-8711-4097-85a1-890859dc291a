<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { isValid, format, parse, isSameDay } from 'date-fns';
import { useDateTimeRange } from '@/uses/date-time-range';

const props = withDefaults(defineProps<{
  disabled?: boolean;
  disabledEndTime?: boolean;
  transfer?: boolean;
  startDate: Date | null | string;
  startTime: string | null;
  endDate: Date | null | string;
  endTime: string | null;
  dateWidth?: number;
  timeWidth?: number;
  limitMin?: any;
  limitMax?: any;
  startTimeSteps?: number[];
  endTimeSteps?: number[];
}>(), {
  disabled: false,
  disabledEndTime: false,
  transfer: false,
  startDate: null,
  startTime: null,
  endDate: null,
  endTime: null,
  dateWidth: 140,
  timeWidth: 100,
  limitMin: null,
  limitMax: null,
  startTimeSteps: () => [],
  endTimeSteps: () => [],
});

const emit = defineEmits([
  'update:startDate',
  'update:startTime',
  'update:endDate',
  'update:endTime',
  'on-change',
]);

const endDatePickerRef = ref();

const model = reactive({
  startDate: null, // 值为日期
  startTime: null, // 值为字符串，格式为HH:mm
  endDate: null, // 值为日期
  endTime: null, // 值为字符串，格式为HH:mm
});
const dateTimeRange = useDateTimeRange(
  {
    min: () => model.startDate,
    max: () => model.endDate,
    isSameDay: () => isSameDay(model.startDate, model.endDate),
  },
  {
    min: () => props.limitMin,
    max: () => props.limitMax,
  },
);


const dateStyle = computed(() => {
  const width = Number.isFinite(props.dateWidth) ? props.dateWidth : 140;
  return { width: `${width}px` };
});

const timeStyle = computed(() => {
  const width = Number.isFinite(props.timeWidth) ? props.timeWidth : 100;
  return { width: `${width}px` };
});


watch(
  () => props.startDate,
  (val) => {
    model.startDate = val;
    emit('on-change');
  },
  {
    immediate: true,
  },
);

watch(
  () => props.startTime,
  (val) => {
    model.startTime = val;
    emit('on-change');
  },
  {
    immediate: true,
  },
);

watch(
  () => props.endDate,
  (val) => {
    model.endDate = val;
    emit('on-change');
  },
  {
    immediate: true,
  },
);

watch(
  () => props.endTime,
  (val) => {
    model.endTime = val;
    emit('on-change');
  },
  {
    immediate: true,
  },
);


function getStartDatetime(date, time) {
  if (!(date instanceof Date)) {
    return null;
  }

  const s = `${format(date, 'yyyy-MM-dd')} ${time || '00:00'}:00`;
  const d = parse(s, 'yyyy-MM-dd HH:mm:ss', new Date());
  if (!isValid(d)) {
    return null;
  }

  return d;
}

function onStartDateInput(val) {
  emit('update:startDate', val);
}

function onStartDateChange(val) {
  nextTick(() => {
    if (val) {
      const startDate = parse(val, 'yyyy-MM-dd', new Date());
      model.startDate = getStartDatetime(startDate, model.startTime);
      emit('update:startDate', model.startDate);

      if (!model.startTime) {
        model.startTime = '00:00';
        emit('update:startTime', model.startTime);
      }
    }

    if (!model.endDate) {
      endDatePickerRef.value.focus();
      endDatePickerRef.value.handleFocus();
    }
  });
}

function onStartDateClear() {
  model.startDate = null;
  emit('update:startDate', model.startDate);
}


function onStartTimeInput(val) {
  emit('update:startTime', val);
}

function onStartTimeChange(val) {
  if (val && model.startDate) {
    model.startDate = getStartDatetime(model.startDate, val);
    emit('update:startDate', model.startDate);
    emit('update:startTime', val);
  }
}

function onStartTimeClear() {
  model.startTime = '';
  emit('update:startTime', model.startTime);

  nextTick(() => {
    if (model.startDate) {
      model.startDate = getStartDatetime(model.startDate, model.startTime);
      emit('update:startDate', model.startDate);
    }
  });
}

function onEndDateInput(val) {
  emit('update:endDate', val);
}

function getEndDatetime(date, time) {
  if (!(date instanceof Date)) {
    return null;
  }

  const s = `${format(date, 'yyyy-MM-dd')} ${time || '23:59'}:59`;
  const d = parse(s, 'yyyy-MM-dd HH:mm:ss', new Date());
  if (!isValid(d)) {
    return null;
  }

  return d;
}

function onEndDateChange(val) {
  nextTick(() => {
    if (val) {
      const endDate = parse(val, 'yyyy-MM-dd', new Date());
      model.endDate = getEndDatetime(endDate, model.endTime);
      emit('update:endDate', model.endDate);

      if (!model.endTime) {
        model.endTime = '23:59';
        emit('update:endTime', model.endTime);
      }
    }
  });
}

function onEndDateClear() {
  model.endDate = null;
  emit('update:endDate', model.endDate);
}

function onEndTimeInput(val) {
  emit('update:endTime', val);
}

function onEndTimeChange(val) {
  nextTick(() => {
    if (val && model.endDate) {
      model.endDate = getEndDatetime(model.endDate, val);
      emit('update:endDate', model.endDate);
      emit('update:endTime', val);
    }
  });
}

function onEndTimeClear() {
  model.endTime = '';
  emit('update:endTime', model.endTime);

  nextTick(() => {
    if (model.endDate) {
      model.endDate = getEndDatetime(model.endDate, model.endTime);
      emit('update:endDate', model.endDate);
    }
  });
}

</script>


<template>
  <div class="date-time-range">
    <DatePicker
      :model-value="model.startDate"
      type="date"
      format="yyyy-MM-dd"
      class="pima-date-picker"
      :disabled="disabled"
      :placeholder="$t('common.placeholder.startDate')"
      :style="dateStyle"
      :options="dateTimeRange.minDateOptions"
      :transfer-class-name="'pima-modal-wrapper pima-date-picker'"
      :transfer="transfer"
      :editable="false"
      @input="onStartDateInput"
      @on-change="onStartDateChange"
      @on-clear="onStartDateClear"
    />
    <TimePicker
      :model-value="model.startTime"
      type="time"
      format="HH:mm"
      class="pima-date-picker ml-4"
      :disabled="disabled"
      :placeholder="$t('common.placeholder.startTime')"
      :style="timeStyle"
      :disabled-hours="dateTimeRange.calcMinDateDisabledHours(model.endTime)"
      :disabled-minutes="dateTimeRange.calcMinDateDisabledMinutes(model.startTime, model.endTime)"
      :steps="startTimeSteps"
      :transfer-class-name="'pima-modal-wrapper pima-date-picker'"
      :transfer="transfer"
      :editable="false"
      @input="onStartTimeInput"
      @on-change="onStartTimeChange"
      @on-clear="onStartTimeClear"
    />

    <span class="joiner">~</span>

    <DatePicker
      ref="endDatePickerRef"
      type="date"
      format="yyyy-MM-dd"
      class="pima-date-picker"
      :disabled="disabled || disabledEndTime"
      :placeholder="$t('common.placeholder.endDate')"
      :model-value="model.endDate"
      :style="dateStyle"
      :options="dateTimeRange.maxDateOptions"
      :transfer-class-name="'pima-modal-wrapper pima-date-picker'"
      :transfer="transfer"
      :editable="false"
      @input="onEndDateInput"
      @on-change="onEndDateChange"
      @on-clear="onEndDateClear"
    />
    <TimePicker
      :model-value="model.endTime"
      type="time"
      format="HH:mm"
      class="pima-date-picker ml-4"
      :placeholder="$t('common.placeholder.endTime')"
      :style="timeStyle"
      :disabled="disabled || disabledEndTime"
      :disabled-hours="dateTimeRange.calcMaxDateDisabledHours(model.startTime)"
      :disabled-minutes="dateTimeRange.calcMaxDateDisabledMinutes(model.startTime, model.endTime)"
      :steps="endTimeSteps"
      :transfer-class-name="'pima-modal-wrapper pima-date-picker'"
      :transfer="transfer"
      :editable="false"
      @input="onEndTimeInput"
      @on-change="onEndTimeChange"
      @on-clear="onEndTimeClear"
    />
  </div>
</template>


<style lang="less" scoped>
.date-time-range {
  // display: flex;
  // align-items: center;

  .joiner {
    padding: 0 10px;
  }
}
</style>
