<template>
  <div class="text-with-poptip">
    <div class="text">
      {{ text }}
    </div>
    <BubbleTips
      v-bind="$attrs"
    >
      <slot />
      <template #content>
        <slot name="content" />
      </template>
    </BubbleTips>
  </div>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';

import BubbleTips from './bubble-tips.vue';


export default defineComponent({
  name: 'TextWithPoptip',

  components: {
    BubbleTips,
  },

  props: {
    text: {
      type: String,
      default: '',
    },
  },

  setup() {
    return {};
  },
});
</script>


<style lang="less" scoped>
.text-with-poptip {
  display: inline-flex;
  justify-content: flex-end;

  .text {
    margin-right: 5px;
  }
}
</style>
