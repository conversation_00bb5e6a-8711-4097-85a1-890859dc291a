import { isValid, startOfDay, isBefore, isAfter } from 'date-fns';


/**
 * @typedef { Object } GetMinAndMax
 * @property { Function } min - 获取开始日期
 * @property { Function } max - 获取结束日期
 * @property { Function } isSameDay - 获取开始日期与结束日期是否为同一天
 */

/**
 * 返回2个disabledDate函数，用于限制最小和最大日期选择
 * @param { GetMinAndMax } range - 获取选择的时间范围
 * @param { GetMinAndMax } limit - 获取限制的时间范围
 */
export function useDateTimeRange(range, limit) {
  function isBeforeDay(date1, date2) {
    if (!(isValid(date1) && isValid(date2))) {
      return false;
    }

    const d1 = startOfDay(date1);
    const d2 = startOfDay(date2);

    return isBefore(d1, d2);
  }

  function isAfterDay(date1, date2) {
    if (!(isValid(date1) && isValid(date2))) {
      return false;
    }

    const d1 = startOfDay(date1);
    const d2 = startOfDay(date2);

    return isAfter(d1, d2);
  }

  // 检查当前日期是否小于最小日期
  function isBeforeMinDate(date, getMin) {
    if (typeof getMin !== 'function') {
      return false;
    }

    const min = getMin();
    return isValid(min) && isBeforeDay(date, min);
  }

  // 检查当前日期是否大于最大日期
  function isAfterMaxDate(date, getMax) {
    if (typeof getMax !== 'function') {
      return false;
    }

    const max = getMax();
    return isValid(max) && isAfterDay(date, max);
  }

  function timeToParts(time) {
    return time.split(':').map((s) => Number(s));
  }

  return {
    minDateOptions: {
      disabledDate(date) {
        return isBeforeMinDate(date, limit?.min)
          || isAfterMaxDate(date, limit?.max)
          || isAfterMaxDate(date, range.max);
      },
    },

    calcMinDateDisabledHours(maxTime) {
      if (typeof range.isSameDay !== 'function' || !range.isSameDay() || !maxTime) {
        return [];
      }

      const [hours] = timeToParts(maxTime);
      const disabledHours = [];
      for (let i = hours + 1; i < 24; i += 1) {
        disabledHours.push(i);
      }

      return disabledHours;
    },

    calcMinDateDisabledMinutes(minTime, maxTime) {
      if (typeof range.isSameDay !== 'function' || !range.isSameDay() || !minTime || !maxTime) {
        return [];
      }

      const [minHours] = timeToParts(minTime);
      const [maxHours, maxMinutes] = timeToParts(maxTime);
      if (minHours !== maxHours) {
        return [];
      }

      const disabledMinutes = [];
      for (let i = maxMinutes + 1; i < 60; i += 1) {
        disabledMinutes.push(i);
      }

      return disabledMinutes;
    },

    maxDateOptions: {
      disabledDate(date) {
        return isBeforeMinDate(date, limit?.min)
          || isAfterMaxDate(date, limit?.max)
          || isBeforeMinDate(date, range.min);
      },
    },

    calcMaxDateDisabledHours(minTime) {
      if (typeof range.isSameDay !== 'function' || !range.isSameDay() || !minTime) {
        return [];
      }

      const [hours] = timeToParts(minTime);
      const disabledHours = [];
      for (let i = 0; i <= hours - 1; i += 1) {
        disabledHours.push(i);
      }

      return disabledHours;
    },

    calcMaxDateDisabledMinutes(minTime, maxTime) {
      if (typeof range.isSameDay !== 'function' || !range.isSameDay() || !minTime || !maxTime) {
        return [];
      }

      const [minHours, minMinutes] = timeToParts(minTime);
      const [maxHours] = timeToParts(maxTime);
      if (minHours !== maxHours) {
        return [];
      }

      const disabledMinutes = [];
      for (let i = 0; i < minMinutes; i += 1) {
        disabledMinutes.push(i);
      }

      return disabledMinutes;
    },
  };
}
