{
  "compilerOptions": {
    "target": "ES5",
    "module": "CommonJS",
    "strict": false,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "Node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowJs": true,
    "noEmit": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "~/*": ["node_modules/*"],
      "^/*": ["./*"],
    },
    "lib": ["es2015", "esnext", "dom", "dom.iterable", "scripthost"]
  },
  "types": [
    "vue",
    "node",
    "webpack-env",
  ],
  "include": [
    "server/**/*.ts",
    "src/**/*.ts",
    "src/**/*.vue",
    "types/*.d.ts"
  ],
  "exclude": [
    "node_modules",
  ],
}
