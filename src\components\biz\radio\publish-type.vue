<script setup lang="ts">
import { PublishType } from '@/consts/job-post';
import { getPublishTypeText } from '@/helps/i18n/publish-type';


</script>


<template>
  <RadioGroup
    v-bind="$attrs"
    class="pima-radio-default"
  >
    <Radio
      v-for="value of Object.values(PublishType)"
      :key="value"
      :label="value"
    >
      {{ getPublishTypeText(value) }}
    </Radio>

    <slot />
  </RadioGroup>
</template>
