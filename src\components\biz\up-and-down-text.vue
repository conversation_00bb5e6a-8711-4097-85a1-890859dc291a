<script setup lang="ts">
import { EmptyText } from '@/consts/empty-text';


interface Props {
  up?:string,
  down?:string,
}

withDefaults(defineProps<Props>(), {
  up: undefined,
  down: undefined,
});


</script>


<template>
  <div
    v-if="up || down"
    class="up-and-down-text"
  >
    <span class="text-theme-1">{{ up || EmptyText }}</span>
    <br>
    <span class="text-theme-2">{{ down || EmptyText }}</span>
  </div>

  <span v-else>{{ EmptyText }}</span>
</template>
