import _ from 'lodash';

import type { SearchSimpleModelType } from '^/types/job-post';
import type { PaginationParamsOption } from '@/helps/api';

import { dateFormatSTZ } from '@/helps/date';
import { namespaceT } from '@/helps/namespace-t';
import { isNothing } from '@/utils/is';
import { PublishType } from '@/consts/job-post';
import { createFormModel } from './models';

type ListParamsType = SearchSimpleModelType & PaginationParamsOption;

/** 处理时间数据为 YYYY-MM-DD */
export const formateToDate = (date: string | Date) => {
  const t = namespaceT('dateFormat');
  return dateFormatSTZ(new Date(date), t('date'));
};

/** 处理列表接口参数 */
export const handleListParams = (params:ListParamsType) => {
  const cloneParams = _.cloneDeep(params);


  cloneParams.creStartTime = !isNothing(cloneParams.creStartTime) ? formateToDate(cloneParams.creStartTime) : undefined;
  cloneParams.creEndTime = !isNothing(cloneParams.creEndTime) ? formateToDate(cloneParams.creEndTime) : undefined;

  return cloneParams;
};


/** 处理表单提交参数 */
export const handleFormParams = (data) => {
  const cloneData = _.cloneDeep(data);

  /** 处理 实践地址 级联数据 */
  Object.assign(cloneData, {
    regionProvince: cloneData.name1[0],
    regionCity: cloneData.name1[1],
    regionDistrict: cloneData.name1[2],
  });
  delete cloneData.name1;

  /** 处理 报名截止时间  定时发布时间 */
  Object.assign(cloneData, {
    deadline: formateToDate(cloneData.deadline),
    publishTime: formateToDate(cloneData.publishTime),
  });

  if (cloneData.publishType === PublishType.Immediately) {
    delete cloneData.publishTime;
  }

  return cloneData;
};


/** 处理详情数据 */
export const handleDetailData = (data) => {
  const cloneData = _.cloneDeep(data);

  cloneData.name1 = [cloneData.regionProvince, cloneData.regionCity];
  if (cloneData.regionDistrict) {
    cloneData.name1.push(cloneData.regionDistrict);
  }

  return _.pick(cloneData, Object.keys(createFormModel()));
};
