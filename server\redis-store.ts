module.exports = function createRedisStore(options) {
  const { default: RedisStore } = require('connect-redis');

  const { redisClient, redisTtl } = options;

  if (!redisClient) {
    throw new Error('[redis-store] redisClient not configured');
  }

  if (!redisTtl) {
    throw new Error('[redis-store] redisTtl not configured');
  }

  const redisStore = new RedisStore({
    client: redisClient,
    ttl: redisTtl, // Session有效期
  });

  return redisStore;
};
