import { reactive, UnwrapRef } from 'vue';
import _ from 'lodash';


export function createRecord<T extends Record<string, unknown> = Record<string, unknown>>(initial = {} as T) {
  // FIXME：特殊处理，方便重置reactive对象
  const state = reactive<{
    model: T;
  }>({
    model: {} as T,
  });

  function set(k: string, v: unknown) {
    const m = state.model as Record<string, unknown>;
    m[k] = v;
  }

  function append(source: Record<string, unknown>) {
    Object.entries(source).forEach(([k, v]) => {
      set(k, v);
    });
  }

  function get(k: string) {
    return state.model[k];
  }

  function keys() {
    return Object.keys(state.model);
  }

  function clear() {
    state.model = {} as UnwrapRef<T>;
  }

  function toObj() {
    return _.clone(state.model);
  }

  append(initial);

  return {
    set,
    append,
    get,
    keys,
    clear,
    toObj,
  };
}
