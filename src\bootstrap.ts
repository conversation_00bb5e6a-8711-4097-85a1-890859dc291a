import 'core-js/stable';
import { createApp } from './main';


// 从全局变量中获取Pinia的初始状态，该状态由服务器端渲染生成并注入到HTML中
const piniaStateValue = window.__INITIAL_STATE__;

// 解构出国际化设置和登录状态，用于应用配置
const { locale } = piniaStateValue.i18n; // 获取国际化区域设置
const { loggedIn } = piniaStateValue.loginStatus; // 获取用户登录状态

// 使用解构出的配置项创建应用实例，包括应用核心、路由器及Pinia状态管理器
const { app, router, pinia } = createApp({ locale, loggedIn });
// 将从服务器接收到的完整状态应用于Pinia，以便客户端状态与服务器端保持一致
pinia.state.value = piniaStateValue;

// 等待路由准备就绪，确保所有异步路由组件和守卫已被解析
router.isReady().then(() => {
  // 当路由准备完毕，挂载应用到DOM中的'#app'元素，此时应用开始在客户端运行
  app.mount('#app');
});
