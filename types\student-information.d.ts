import { YoN } from '@/consts/y-o-n';
import type { AttachmentVO } from './attachment';

export interface SearchSimpleModelType {
  keyword?: string;
  ids?: number[];
  facId?: number;
  clsId?: number;
}


/**
 * StudentRecordVO，学生资料VO
 */
export interface StudentInfoListItemType {
  /**
     * 班级ID
     */
  clsId?: number;
  /**
     * 班级名称
     */
  clsName?: string;
  /**
     * 研究院Id
     */
  facId?: number;
  /**
     * 研究院名称
     */
  facName?: string;
  /**
     * 籍贯-市名称
     */
  householdAddrCityName?: string;
  /**
     * 籍贯-省code
     */
  householdAddrProvCode?: string;
  /**
     * 籍贯-省名称
     */
  householdAddrProvName?: string;
  /**
     * id
     */
  id?: number;
  /**
     * 专业
     */
  major?: string;
  /**
     * 手机号
     */
  mobile?: string;
  /**
     * 姓名
     */
  name?: string;
  /**
     * 硕博类型
     */
  pgType?: string;
  /**
     * 性别 数据字典：USER_SEX
     */
  sex?: string;
  /**
     * 是否有学籍  枚举（是：Y，否：N）
     */
  studyStatusInd?: string;
  /**
     * 操作人
     */
  updName?: string;
  /**
     * 操作时间
     */
  updTime?: Date;
  /**
     * 学号
     */
  userNo?: string;
}


/**
 * StudentRecordDetailsVO，学生资料详情VO
 */
export interface StudentRecordDetailsVO {
  // 简历附件
  annexVO?: StudentRecordDetailsAnnexVO;
  // 银行财务信息
  bankInfos?: BankInfoDetailsVO[];
  // 基本信息
  basicVO?: StudentRecordDetailsBasicVO;
  // 亲属
  emergencyContacts?: EmergencyContactDetailsVO[];
  // 荣誉经历
  honorsVO?: StudentRecordDetailsHonorsVO;
  // 参与活动
  activityList?: StudentRecordDetailsActivityVO[];
  id?: number;
  // 学校信息
  schoolInfoVO?: StudentRecordDetailsSchoolInfoVO;
}

/**
 * StudentRecordDetailsActivityVO，参与活动
 */
export interface StudentRecordDetailsActivityVO {
  /**
     * 活动分类 数据字典[ACTIVITY_CATEGORY_CODE，应用编码：tsinghua-activity]
     */
  categoryCode?: string;
  /**
     * 活动结束时间 yyyy-MM-dd HH:mm:ss
     */
  endTime?: Date;
  /**
     * 活动地点
     */
  location?: string;
  /**
     * 活动开始时间 yyyy-MM-dd HH:mm:ss
     */
  startTime?: Date;
  /**
     * 活动名称
     */
  title?: string;
}

/**
 * StudentRecordDetailsAnnexVO，简历附件
 */
export interface StudentRecordDetailsAnnexVO {
  /**
     * 简历附件
     */
  resumes?: AttachmentVO[];
  /**
     * 科研经历
     */
  teacherKnows?: AttachmentVO[];
}


/**
 * BankInfoDetailsVO，银行财务信息
 */
export interface BankInfoDetailsVO {
  /**
     * 银行卡号
     */
  bankAccount?: string;
  /**
     * 支行名称
     */
  bankBranchName?: string;
  /**
     * 银行卡所属用户姓名
     */
  bankCardUserName?: string;
  /**
     * 银行城市
     */
  bankCity?: string;
  /**
     * 银行全称
     */
  bankName?: string;
  /**
     * 银行省份
     */
  bankProvince?: string;
  /**
     * 主键ID
     */
  id?: number;
  /**
     * 用户信息ID
     */
  userProfileId?: number;
  /**
     * 开户银行
     */
  depositBank?: string;
}

/**
 * StudentRecordDetailsBasicVO，基本信息
 */
export interface StudentRecordDetailsBasicVO {
  /**
     * 现住址
     */
  address?: string;
  /**
     * 家庭地址-区code
     */
  baseAddrAreaCode?: string;
  /**
     * 家庭地址-区ID
     */
  baseAddrAreaId?: number;
  /**
     * 家庭地址-区名称
     */
  baseAddrAreaName?: string;
  /**
     * 家庭地址-市code
     */
  baseAddrCityCode?: string;
  /**
     * 家庭地址-市ID
     */
  baseAddrCityId?: number;
  /**
     * 家庭地址-市名称
     */
  baseAddrCityName?: string;
  /**
     * 家庭地址-省code
     */
  baseAddrProvCode?: string;
  /**
     * 家庭地址-省ID
     */
  baseAddrProvId?: number;
  /**
     * 家庭地址-省名称
     */
  baseAddrProvName?: string;
  /**
     * 班级ID
     */
  clsId?: number;
  /**
     * 班级名称
     */
  clsName?: string;
  /**
     * 生源地
     */
  birthAddress?: string;
  /**
     * 班导师ID
     */
  clsSupervisorId?: number;
  /**
     * 班导师姓名
     */
  clsSupervisorName?: string;
  /**
     * 班导师工号
     */
  clsSupervisorNo?: string;
  /**
     * 结业方式
     */
  completionMethod?: string;
  /**
     * 邮箱
     */
  email?: string;
  /**
     * 系名称
     */
  facDeptName?: string;
  /**
     * 研究院ID
     */
  facId?: number;
  /**
     * 研究院名称
     */
  facName?: string;
  /**
     * 家庭详细地址
     */
  homeAddress?: string;
  /**
     * 家庭电话
     */
  homePhone?: string;
  /**
     * 籍贯-区code
     */
  householdAddrAreaCode?: string;
  /**
     * 籍贯-区Id
     */
  householdAddrAreaId?: number;
  /**
     * 籍贯-区名称
     */
  householdAddrAreaName?: string;
  /**
     * 籍贯-市code
     */
  householdAddrCityCode?: string;
  /**
     * 籍贯-市Id
     */
  householdAddrCityId?: number;
  /**
     * 籍贯-市名称
     */
  householdAddrCityName?: string;
  /**
     * 籍贯-国家code
     */
  householdAddrCountryCode?: string;
  /**
     * 籍贯-国家Id
     */
  householdAddrCountryId?: number;
  /**
     * 籍贯-国家名称
     */
  householdAddrCountryName?: string;
  /**
     * 籍贯-省code
     */
  householdAddrProvCode?: string;
  /**
     * 籍贯-省Id
     */
  householdAddrProvId?: number;
  /**
     * 籍贯-省名称
     */
  householdAddrProvName?: string;
  /**
     * 证件号码
     */
  idcard?: string;
  /**
     * 证件类型
     */
  idcardType?: string;
  /**
     * 专业
     */
  major?: string;
  /**
     * 婚姻状态 数据字典：USER_PROFILE_MARITAL_STATUS
     */
  maritalStatus?: string;
  /**
     * 手机
     */
  mobile?: string;
  /**
     * 手机区号
     */
  mobileAreaCode?: string;
  /**
     * 姓名
     */
  name?: string;
  /**
     * 民族
     */
  nation?: string;
  /**
     * 硕博类型
     */
  pgType?: string;
  /**
     * 政治面貌
     */
  politic?: string;
  /**
     * 性别 数据字典：USER_SEX
     */
  sex?: string;
  /**
     * 特长
     */
  specialSkill?: string;

  /**
     * 学生助理ID
     */
  clsAssistantId?: number;
  /**
     * 学生助理工号
     */
  clsAssistantNo?: number;

  /**
     * 学生助理姓名
     */
  clsAssistantName?: string;


  /**
     * 学生组副组长ID
     */
  studGrpDeputyLeaderId?: number;
  /**
     * 学生组副组长姓名
     */
  studGrpDeputyLeaderName?: string;
  /**
     * 学生组副组长工号
     */
  studGrpDeputyLeaderNo?: string;
  /**
     * 学生组组长ID
     */
  studGrpLeaderId?: number;
  /**
     * 学生组组长姓名
     */
  studGrpLeaderName?: string;
  /**
     * 学生组组长工号
     */
  studGrpLeaderNo?: string;
  /**
     * 是否有学籍 枚举（是：Y，否：N）
     */
  studyStatusInd?: YoN;
  /**
     * 来深时间 yyyy-MM-dd HH:mm:ss
     */
  szDate?: Date;
  /**
     * 导师ID
     */
  teacherId?: number;
  /**
     * 导师姓名
     */
  teacherName?: string;
  /**
     * 导师工号
     */
  teacherNo?: string;
  /**
     * 学号
     */
  userNo?: string;
}

/**
 * EmergencyContactDetailsVO，亲属
 */
export interface EmergencyContactDetailsVO {
  /**
     * 紧急联系人姓名
     */
  contactName?: string;
  /**
     * 紧急联系人电话   aes加密
     */
  contactPhone?: string;
  /**
     * 主键ID
     */
  id?: number;
  /**
     * 关系     紧急联系人与用户关系 数据字典：EMERGENCY_CONTACT_RELATIVE
     */
  relative?: string;
  /**
     * 用户信息ID
     */
  userProfileId?: number;
}

/**
 * StudentRecordDetailsHonorsVO，荣誉/经历
 */
export interface StudentRecordDetailsHonorsVO {
  /**
     * 奖励与荣誉
     */
  awards?: string;
  /**
     * 实习经历
     */
  internshipExp?: string;
  /**
     * 其他经历
     */
  othExp?: string;
  /**
     * 科研经历
     */
  researchExp?: string;
}

/**
 * StudentRecordDetailsSchoolInfoVO，学校信息
 */
export interface StudentRecordDetailsSchoolInfoVO {
  /**
     * 校园卡号
     */
  cardid?: string;
  /**
     * 现任职务
     */
  currentJobTitle?: string;
  /**
     * 宿舍地址
     */
  dormAddress?: string;
  /**
     * 宿舍电话
     */
  dormTel?: string;
  /**
     * 本科院校
     */
  graduateSchool?: string;
  /**
     * 实验室地址
     */
  labAddr?: string;
  /**
     * 所在实验室
     */
  labName?: string;
  /**
     * 实验室电话
     */
  labPhone?: string;
  /**
     * 曾任职务
     */
  pastJobTitle?: string;
  /**
     * 备注
     */
  remark?: string;
  /**
     * 本科专业
     */
  speciality?: string;
  /**
     * 导师ID
     */
  teacherId?: number;
  /**
     * 导师姓名
     */
  teacherName?: string;
  /**
     * 导师工号
     */
  teacherNo?: string;
}


/**
 * 编辑表单modelType
 *
 */

export interface RemotePeopleModelType {
  id:number,
  name:string,
  [key:string]:unknown
}

export interface EditFormModelType {
  /** 研究院 */
  facId:number,
  /** 班级 */
  clsId:number,
  /** 班导师 */
  clsSupervisor:RemotePeopleModelType,
  /** 学生组组长 */
  studGrpLeader:RemotePeopleModelType,
  /** 学生组副组长 */
  studGrpDeputyLeader:RemotePeopleModelType,
  /** 学生助理 */
  clsAssistant:RemotePeopleModelType,
}


/** 编辑详情 */
export interface DetailForEditType {
  /**
     * 带班助理ID
     */
  clsAssistantId?: number;
  /**
     * 带班助理姓名
     */
  clsAssistantName?: string;
  /**
     * 带班助理工号
     */
  clsAssistantNo?: string;
  /**
     * 班级ID
     */
  clsId?: number;
  /**
     * 班级名字
     */
  clsName?: string;
  /**
     * 班导师ID
     */
  clsSupervisorId?: number;
  /**
     * 班导师姓名
     */
  clsSupervisorName?: string;
  /**
     * 班导师工号
     */
  clsSupervisorNo?: string;
  /**
     * 研究院ID
     */
  facId?: number;
  /**
     * 研究院名称
     */
  facName?: string;
  /**
     * 学生组副组长ID
     */
  studGrpDeputyLeaderId?: number;
  /**
     * 学生组副组长姓名
     */
  studGrpDeputyLeaderName?: string;
  /**
     * 学生组副组长工号
     */
  studGrpDeputyLeaderNo?: string;
  /**
     * 学生组组长ID
     */
  studGrpLeaderId?: number;
  /**
     * 学生组组长姓名
     */
  studGrpLeaderName?: string;
  /**
     * 学生组组长工号
     */
  studGrpLeaderNo?: string;
}


export type StudentInfoDetailType = StudentRecordDetailsAnnexVO & {
  // 银行财务信息
  bankInfos?: BankInfoDetailsVO[];
  // 基本信息
  basicVO?: StudentRecordDetailsBasicVO;
  // 亲属
  emergencyContacts?: EmergencyContactDetailsVO[];
  // 荣誉经历
  honorsVO?: StudentRecordDetailsHonorsVO;
  // 参与活动
  activityList?: StudentRecordDetailsActivityVO[];
  id?: number;
  // 学校信息
  schoolInfoVO?: StudentRecordDetailsSchoolInfoVO;
};
