<script lang="ts" setup>
import PimaModal from '@/components/common/pima-modal.vue';


withDefaults(defineProps<{
  title?:string,
  content?:string
  loading?:boolean
}>(), {
  title: '',
  content: '',
  loading: false,
});

const emit = defineEmits<{
  'on-confirm': [],
  'on-cancel': [],
}>();

const onCancel = () => {
  emit('on-cancel');
};

const onConfirm = () => {
  emit('on-confirm');
};


</script>


<template>
  <PimaModal
    :title="title"
    :width="480"
    :loading="loading"
    v-bind="$attrs"
    :closable="false"
    @cancel="onCancel"
    @confirm="onConfirm"
  >
    <div class="content">
      <Icon
        type="ios-alert-outline"
        class="icon-warning"
      />
      <div class="description">
        <slot>{{ content }}</slot>
      </div>
    </div>
    <template
      v-if="$slots.footer"
      #footer
    >
      <slot name="footer" />
    </template>
  </PimaModal>
</template>


<style lang="less" scoped>
.content {
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-warning {
    width: 44px;
    color: var(--primary-color);
    font-size: 44px;
  }

  .description {
    margin-left: 15px;
    color: fade(#000, 85%);
    font-weight: normal;
    font-size: 16px;
  }


}
</style>
