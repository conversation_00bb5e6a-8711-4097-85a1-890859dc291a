<template>
  <Poptip
    v-bind="$attrs"
    :content="textContent"
    :trigger="trigger"
    :placement="placement"
    class="bubble-tips"
  >
    <slot v-if="$slots.default" />
    <Icon
      v-else
      type="ios-alert-outline"
      color="var(--primary-color)"
      size="16"
    />

    <template #content>
      <slot name="content" />
    </template>
  </Poptip>
</template>

<script lang='ts'>
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'BubbleTips',

  props: {
    content: {
      type: [String, Array],
      default: '',
    },

    trigger: {
      type: String,
      default: 'hover',
    },

    placement: {
      type: String,
      default: 'right',
    },
  },

  setup(props) {
    const textContent = computed(() => {
      if (Array.isArray(props.content)) {
        return props.content.join('\n');
      }

      return props.content;
    });

    return {
      textContent,
    };
  },
});
</script>

<style lang="less" scoped>
.bubble-tips {
  display: inline-flex;

  :deep(.ivu-poptip-rel) {
    display: inline-flex;
    white-space: pre-line;
    word-break: break-word;
  }

  :deep(.ivu-poptip-body-content-inner) {
    white-space: pre-line;
    text-align: left;
  }
}
</style>
