import { ref } from 'vue';
import { defineStore } from 'pinia';
import { CascaderRegionsItem, RegionsApi } from '@/api/client/regions';


export const useRegionsStore = defineStore('regionsStatus', () => {
  const regions = ref<CascaderRegionsItem[]>([]);

  const fetch = async (id:number = undefined) => {
    if (!id && regions.value.length > 0) {
      return null;
    }

    const api = new RegionsApi();
    if (id) {
      api.params = {
        parentId: id,
      };
    }

    const res = await api.sendForRegions();

    if (Array.isArray(regions.value) && regions.value.length === 0) {
      regions.value = res;
    }

    return res;
  };


  return {
    regions,

    fetch,
  };
});
