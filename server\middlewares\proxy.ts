module.exports = function createProxyMiddlewares(options) {
  const { createProxyMiddleware, fixRequestBody, responseInterceptor } = require('http-proxy-middleware');

  const { proxyTable } = options;
  const proxyMiddlewares = [];
  Object.keys(proxyTable).forEach((key) => {
    const isString = typeof proxyTable[key] === 'string';
    const proxyMiddlewareOptions = isString ? { target: proxyTable[key] } : proxyTable[key];
    const proxyMiddleware = createProxyMiddleware(key, {
      ...proxyMiddlewareOptions,
      selfHandleResponse: true,
      onProxyReq(proxyReq, req) {
        fixRequestBody(proxyReq, req);
        const sessionId = req.session ? req.session.id : '-';
        // eslint-disable-next-line no-console
        console.log(
          '[HPM]',
          new Date().toISOString(),
          'onProxyReq->req',
          req.method,
          req.url,
          sessionId,
          req.cookies,
          req.headers.authorization,
          req.body,
        );
      },
      onProxyRes: responseInterceptor(async (responseBuffer, proxyRes, req) => {
        const response = responseBuffer.toString('utf8');
        if (response && response.includes('"success":false')) {
          const sessionId = req.session ? req.session.id : '-';
          // eslint-disable-next-line no-console
          console.log(
            '[HPM]',
            new Date().toISOString(),
            'onProxyRes->responseInterceptor',
            req.method,
            req.url,
            sessionId,
            req.cookies,
            req.headers.authorization,
            req.body,
            response,
          );
        }

        return responseBuffer;
      }),
    });

    proxyMiddlewares.push(proxyMiddleware);
  });

  return proxyMiddlewares;
};
