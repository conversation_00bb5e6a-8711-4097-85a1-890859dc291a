/* eslint-disable @typescript-eslint/naming-convention */
module.exports = function createTokenMiddlewares(options) {
  const axios = require('axios');
  const { nanoid } = require('nanoid');
  const md5 = require('js-md5');
  const URI = require('urijs');
  const qs = require('qs');
  const { pick } = require('lodash');
  const { getRedisValue, setRedisValue } = require('./redis')();

  const ErrorCode = Object.freeze({
    GET_TOKEN_FAILED: 'F_GET_TOKEN_FAILED',
    REFRESH_TOKEN_FAILED: 'F_REFRESH_TOKEN_FAILED',
    LOCK_FAILED: 'F_LOCK_FAILED',
    INVALID_NONCE: 'F_INVALID_NONCE',
    INVALID_SIGNATURE: 'F_INVALID_SIGNATURE',
  });

  const {
    clientId,
    clientSecret,
    baseUrl,
    casUserSessionKey,
    tokenSessionKey,
    redisPrefix,
    redlock,
    redisClient,
    redisTtl,
    apiSalt,
  } = options;
  const config_clientId = clientId;
  const config_clientSecret = clientSecret;
  const config_baseUrl = baseUrl;
  const config_casUserSessionKey = casUserSessionKey;
  const config_tokenSessionKey = tokenSessionKey || 'appToken';
  const config_redisPrefix = redisPrefix || '';
  const config_redisTtl = redisTtl || 60 * 60 * 1; // nonce有效期為1h
  const config_apiSalt = apiSalt || '';

  if (!redisClient) {
    throw new Error('[createTokenMiddlewares] redisClient not configured');
  }

  redlock.on('clientError', (err) => {
    // eslint-disable-next-line no-console
    console.error('A redis error has occurred:', err.stack || '');
  });

  // 申请Token
  // 调用数据中心接口
  function fetchToken(username) {
    const data = {
      clientId: config_clientId,
      nonce: md5(nanoid()),
      timestamp: Date.now(),
      username,
    };

    const dataString = qs.stringify(data, { delimiter: '' });
    const raw = `${dataString}${config_clientSecret}`;
    const signature = md5(raw);
    Object.assign(data, {
      signature,
    });

    const uri = new URI(config_baseUrl);
    uri.segment('oauth/username/token');
    return axios.post(uri.toString(), {}, {
      headers: {
        Accept: 'application/json',
        // 'Content-Type': 'application/x-www-form-urlencoded',
      },
      params: data,
    });
  }

  // 刷新Token
  // 调用数据中心接口
  function fetchRefreshToken(refreshToken_) {
    const data = { refreshToken: refreshToken_ };

    const uri = new URI(config_baseUrl);
    uri.segment('oauth/token/refresh');
    return axios.post(uri.toString(), qs.stringify(data), {
      headers: {
        Accept: 'application/json',
        // 'Content-Type': 'application/x-www-form-urlencoded',
      },
      params: data,
    });
  }

  // Token信息是否已过期
  function isExpired(authInfo) {
    const { expiresIn } = authInfo;
    if (expiresIn) {
      return Date.now() > new Date(expiresIn).getTime();
    }

    return true;
  }

  // 保存Token信息到Session
  function saveAuthTokenToSession(req, authInfo) {
    return new Promise<void>((resolve, reject) => {
      const { tokenType, accessToken, refreshToken: refreshToken_, expiresIn, userCategory } = authInfo;
      const interval = expiresIn * 1000;
      // const interval = 30 * 1000;
      const expires = new Date(Date.now() + interval).toISOString();

      // eslint-disable-next-line no-param-reassign
      req.session[config_tokenSessionKey] = {
        tokenType,
        accessToken,
        refreshToken: refreshToken_,
        expiresIn: expires,
        userCategory,
      };
      req.session.save((error) => {
        if (error) {
          // eslint-disable-next-line no-console
          console.error('Save token to session error', error.stack || '');
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  // 当用户不在登录状态时，返回401状态码
  function blockTokenMiddleware(req, res, next) {
    if (req.xhr) {
      if (!req.session || (req.session && !req.session[config_casUserSessionKey])) {
        res.sendStatus(401);
        return;
      }
    }

    next();
  }

  async function fetchTokenAndStore(req) {
    try {
      const response = await fetchToken(req.session[config_casUserSessionKey]);
      const { success, model } = response.data;
      if (!success) {
        const error = new Error('Get token failed');
        // @ts-expect-error: Property 'response' does not exist on type 'Error'.
        error.response = response;
        throw error;
      }

      await saveAuthTokenToSession(req, model);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Get token error', error.stack || '');
      throw error;
    }
  }

  async function refreshTokenAndStore(req) {
    try {
      const response = await fetchRefreshToken(req.session[config_tokenSessionKey].refreshToken);
      const { success, model } = response.data;
      if (!success) {
        const error = new Error('Refresh token failed');
        // @ts-expect-error: Property 'response' does not exist on type 'Error'.
        error.response = response;
        throw error;
      }

      await saveAuthTokenToSession(req, model);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Refresh token error', error.stack || '');
      throw error;
    }
  }

  function reloadSession(req) {
    return new Promise((resolve, reject) => {
      try {
        req.session.reload(resolve);
      } catch (error) {
        reject(error);
      }
    });
  }

  function clearSessionLoginInfo(req) {
    if (req.session) {
      if (req.session[config_casUserSessionKey]) {
        // eslint-disable-next-line no-param-reassign
        delete req.session[config_casUserSessionKey];
      }

      if (req.session[config_tokenSessionKey]) {
        // eslint-disable-next-line no-param-reassign
        delete req.session[config_tokenSessionKey];
      }
    }
  }

  // 更新Token
  async function renewTokenMiddleware(req, res, next) {
    if (req.session && req.session[config_casUserSessionKey]) {
      if (!req.session[config_tokenSessionKey]) {
        try {
          await fetchTokenAndStore(req);
        } catch (error) {
          clearSessionLoginInfo(req);

          if (req.xhr) {
            if (error.response && error.response.status && error.response.data) {
              res.status(error.response.status);
              res.json(error.response.data);
              res.end();
              return;
            }

            res.status(500);
            res.json({
              success: false,
              errorCode: ErrorCode.GET_TOKEN_FAILED,
            });
            res.end();
            return;
          }

          const err = new Error(error.message);
          // @ts-expect-error: Property 'code' does not exist on type 'Error'.
          err.code = ErrorCode.GET_TOKEN_FAILED; // 请注意，这里是node使用的错误码，不是接口返回的错误码
          next(err);
          return;
        }
      } else if (isExpired(req.session[config_tokenSessionKey])) {
        const resource = `${config_redisPrefix}locks:account:${req.session[config_casUserSessionKey]}`;
        const ttl = 3000;
        try {
          const lock = await redlock.lock(resource, ttl);
          await reloadSession(req);
          if (isExpired(req.session[config_tokenSessionKey])) {
            await refreshTokenAndStore(req);
          }

          await redlock.unlock(lock);
        } catch (error) {
          clearSessionLoginInfo(req);

          if (req.xhr) {
            if (error.response && error.response.status && error.response.data) {
              res.status(error.response.status);
              res.json(error.response.data);
              res.end();
              return;
            }

            res.status(500);
            res.json({
              success: false,
              errorCode: ErrorCode.REFRESH_TOKEN_FAILED,
            });
            res.end();
            return;
          }

          const err = new Error(error.message);
          // @ts-expect-error: Property 'code' does not exist on type 'Error'.
          err.code = ErrorCode.REFRESH_TOKEN_FAILED; // 请注意，这里是node使用的错误码，不是接口返回的错误码
          next(err);
          return;
        }
      }
    }

    next();
  }

  // 替换AJAX请求头部Authorization值
  function replaceRequestTokenMiddleware(req, res, next) {
    if (req.xhr
      && req.session
      && req.session[config_casUserSessionKey]
      && req.session[config_tokenSessionKey]
      && req.session[config_tokenSessionKey].tokenType
      && req.session[config_tokenSessionKey].accessToken) {
      const sess = req.session[config_tokenSessionKey];
      const authorization = `${sess.tokenType} ${sess.accessToken}`;
      Object.assign(req.headers, {
        authorization,
      });
    }

    next();
  }

  function regenerateSession(req) {
    return new Promise<void>((resolve) => {
      req.session.regenerate((error) => {
        if (error) {
          // eslint-disable-next-line no-console
          console.error('Session regenerate error', error.stack || '');
        }

        resolve();
      });
    });
  }

  // 删除Token信息
  async function removeTokenMiddleware(req, res, next) {
    clearSessionLoginInfo(req);
    await regenerateSession(req);
    next();
  }

  // 替换locals变量
  function replaceLocalsTokenMiddleware(req, res, next) {
    if (!req.xhr) {
      if (req.session[config_tokenSessionKey]) {
        Object.assign(res.locals, {
          accessToken: req.session[config_tokenSessionKey].accessToken || '',
        });
      }
    }

    next();
  }


  function getNonceKey(nonce): string {
    return `nonce:${nonce}`;
  }

  async function isNonceExists(nonce) {
    const nonceKey = getNonceKey(nonce);
    const data = await getRedisValue(redisClient, nonceKey);
    return !!data;
  }

  async function saveNonce(nonce) {
    const nonceKey = getNonceKey(nonce);
    setRedisValue(redisClient, nonceKey, 1, config_redisTtl);
  }

  function isSignatureValid(params): boolean {
    const paramsKeys = Object.keys(params).filter((k) => k !== 'signature');
    let raw = '';
    for (let i = 0; i < paramsKeys.length; i += 1) {
      const key = paramsKeys[i];
      raw += `${key}=${params[key]}`;
    }

    raw += config_apiSalt;

    return md5(raw) === params.signature;
  }

  // 获取Token信息
  async function tokenInfoMiddleware(req, res) {
    if (!req.query.nonce) {
      res.send({
        success: false,
        errorCode: ErrorCode.INVALID_NONCE,
      });
      return;
    }

    // 校验nonce参数
    const nonceExists = await isNonceExists(req.query.nonce);
    if (nonceExists) {
      res.send({
        success: false,
        errorCode: ErrorCode.INVALID_NONCE,
      });
      return;
    }

    // 保存nonce参数
    saveNonce(req.query.nonce);

    // 校验签名
    if (!isSignatureValid(req.query)) {
      res.send({
        success: false,
        errorCode: ErrorCode.INVALID_SIGNATURE,
      });
      return;
    }

    if (!req.session[config_tokenSessionKey]) {
      res.send({
        success: false,
        errorCode: ErrorCode.GET_TOKEN_FAILED,
      });
      return;
    }

    res.send({
      success: true,
      model: pick({ ...req.session[config_tokenSessionKey] }, ['tokenType', 'accessToken', 'expiresIn']),
    });
  }

  return {
    blockTokenMiddleware,
    renewTokenMiddleware,
    replaceRequestTokenMiddleware,
    replaceLocalsTokenMiddleware,
    removeTokenMiddleware,
    tokenInfoMiddleware,
  };
};
