<template>
  <Drawer
    :value="value"
    :class-name="themeClass"
    :mask-closable="false"
    :width="width"
    :title="title"
    v-bind="attrs"
    v-on="attrs"
  >
    <div
      ref="modalBodyBlockRef"
      class="pima-drawer-body"
    >
      <div class="pima-drawer-form-wrapper">
        <div class="pima-drawer-content-main">
          <slot />
        </div>
      </div>
    </div>

    <template
      #footer
    >
      <slot
        v-if="slots.footer"
        name="footer"
      />

      <div
        v-else
        class="action"
      >
        <Button
          class="pima-btn"
          @click="onCancel()"
        >
          {{ $t('common.action.cancel') }}
        </Button>

        <Button
          v-if="can"
          type="primary"
          class="pima-btn"
          :loading="loading"
          :disabled="confirmBtnDisabled"
          @click="onConfirm()"
        >
          {{ confirmText }}
        </Button>
      </div>
    </template>

    <Spin
      v-show="contentLoading"
      large
      fix
    />
  </Drawer>
</template>


<script lang='ts'>
import { computed, defineComponent, nextTick, ref, useAttrs, useSlots, watch } from 'vue';

import { namespaceT } from '@/helps/namespace-t';


const tm = namespaceT('common');

const ModalSize = Object.freeze({
  large: 640,
  default: 540,
  small: 480,
});


export default defineComponent({
  name: 'PimaDrawer',

  props: {
    value: Boolean,

    loading: Boolean,

    contentLoading: Boolean, // 主题内容加载中，显示Spin

    size: {
      type: [String, Number],
      default: 'large',
    },

    title: {
      type: String,
      default: undefined,
    },

    confirmText: {
      type: String,
      default: tm('action.confirm'),
    },

    can: {
      type: Boolean,
      default: true,
    },

    className: {
      type: String,
      default: undefined,
    },

    confirmBtnDisabled: {
      type: Boolean,
      default: false,
    },
  },

  emits: [
    'input',
    'confirm',
    'cancel',
  ],

  setup(props, { emit }) {
    const slots = useSlots();

    const modalBodyBlockRef = ref();

    function onCancel() {
      emit('input', false);
      emit('cancel');
    }

    function onConfirm() {
      emit('confirm');
    }

    function getThemeClass() {
      const classList = ['pima-drawer-wrapper'];

      // iview Modal的class-name属性只接受string
      return classList.join(' ').concat(` ${props.className || ''}`);
    }

    const themeClass = computed(() => getThemeClass());

    const width = computed(() => {
      if (typeof props.size === 'number') {
        return props.size;
      }
      return ModalSize[props.size];
    });

    function scrollTop() {
      if (modalBodyBlockRef.value.parentNode.scrollTop > 0) {
        modalBodyBlockRef.value.parentNode.scrollTop = 0;
      }
    }

    watch(() => props.value, (val) => {
      if (val) {
        nextTick(() => {
          scrollTop();
        });
      }
    });

    return {
      attrs: useAttrs(),
      slots,
      themeClass,
      width,
      modalBodyBlockRef,

      onCancel,
      onConfirm,
    };
  },
});
</script>
