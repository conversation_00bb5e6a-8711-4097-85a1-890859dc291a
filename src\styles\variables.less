@inputBorderColor: #EFEFEF;
@defaultBorderColor: #EFEFEF;
@inputBorderColorHover: var(--primary-hover-color);
@btnPrimaryBg: #1F65E0;
@btnPrimaryCorlor: #fff;
@btnBgOverlayHover: rgba(255,255,255,1);
@btnBgOverlayActive: rgba(0,0,0,1);
@titleColor: rgba(0, 0, 0, 0.85);
@textColor: rgba(0, 0, 0, 0.65);
@dangerColor: #D63C3C;
@link-hsvhue: 218;
@link-hsvsaturation: 86%;
@link-hsvvalue: 88%;
@link-hover: 12%;
@link-active: -12%;
@mainColor: #1F65E0;
@assistColor: rgba(254, 236, 230, 0.6);
@switchDeaultColor: #B3B3B3;

@headerHeight: 40px;
@pageHeaderHeight: 50px;
@sideBarWidth: 240px;
@commonTitleFontSize: 16px;
@btnHeight: 30px;
@btnFontSize: 14px;
@formLabelFontSize: 14px;
@formLabelLineHeight: 32px;

@topTreeNodeHeight: 40px;
@treeNodeHeight: 34px;
@treeNodeBg: #f9f9f9;
@treeNodeSelectedBg: #f5f5f5;
@treeNodeSelectedBorderBg: #1F65E0;

@grayBg: #FCFCFC;
