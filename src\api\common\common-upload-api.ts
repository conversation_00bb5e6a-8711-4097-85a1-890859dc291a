import { LOCAL_BDC_DFS_API_BASE_URL, API_SALT } from '@/config/api';
import { LOCALE_COOKIE_KEY } from '@/config/cookie';
import { BaseUploadRequestApi } from '../base/base-upload-request-api';


export class CommonUploadApi<T> extends BaseUploadRequestApi<T> {
  constructor(args?) {
    super({
      baseURL: LOCAL_BDC_DFS_API_BASE_URL,
      salt: API_SALT,
      localeCookieKey: LOCALE_COOKIE_KEY,
      timeout: 0, // 不设置超时
      ...args,
    });
  }
}
