
<script setup lang="ts" generic="T extends { id: number | string }">
defineProps<{
  list: T[],
  maxlength?: number,
  hideAdd?: boolean,
}>();

const emits = defineEmits<{
  'on-add': [item: T, index: number],
  'on-remove': [item: T, index: number],
}>();
</script>


<template>
  <div class="dynamic-wrap">
    <div
      v-for="(item, index) in list"
      :key="item.id"
      class="dynamic-item"
    >
      <div class="content-wrap">
        <slot
          :item="item"
          :index="index"
        />
      </div>

      <div class="action-wrap">
        <Icon
          v-if="!hideAdd && (maxlength ? list.length < maxlength : true)"
          type="ios-add-circle-outline"
          color="#1f65e0"
          class="icon mr-5"
          :size="20"
          @click="emits('on-add', item, index)"
        />

        <Icon
          v-if="(list.length > 1) || hideAdd"
          type="ios-remove-circle-outline"
          color="#d63c3c"
          class="icon"
          :size="20"
          @click="emits('on-remove', item, index)"
        />
      </div>
    </div>
  </div>
</template>


<style scoped lang="less">
.dynamic-wrap {
  width: 100%;

  .dynamic-item {
    display: flex;
    width: 100%;

    > .content-wrap {
      flex: 1;
      margin-right: 5px;
    }

    .action-wrap {
      display: flex;
      width: 60px;
      padding-top: 5px;

      .icon {
        cursor: pointer;
      }
    }
  }

  .dynamic-item + .dynamic-item {
    margin-top: 8px;
  }
}
</style>
