<script setup lang="ts">
import { onBeforeMount } from 'vue';

import DetailLabelItem from '@/components/common/detail-label-item.vue';

import { namespaceT } from '@/helps/namespace-t';
import { usePostTypeStore } from '@/store/data-tags/post-type';
import { formateToDate } from '@/views/job-post/helps/handle-api-data';


const model = defineModel<Record<string, any>>();

const t = namespaceT('jobPost');
const postTypeStore = usePostTypeStore();


onBeforeMount(() => {
  postTypeStore.loadDataIfNeeded();
});

</script>


<template>
  <Row
    :gutter="24"
    class="pl-20"
  >
    <!-- 岗位名称 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.postName')"
        :value="model.name"
      >
        "asda"
      </DetailLabelItem>
    </Col>

    <!-- 岗位类型 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.postType')"
        :value="postTypeStore.getTextByCode(model.postType)"
      />
    </Col>

    <!-- 研究方向 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.researchDirection')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 性别要求 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.sexRemand')"
        :value="model.name"
      />
    </Col>


    <!-- 所需专业 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.neededMajor')"
        :value="model.name"
      />
    </Col>

    <!-- 需求人数 -->
    <Col :span="12">
      <DetailLabelItem
        :label="t('label.numOfPerson')"
        :value="model.name"
      />
    </Col>

    <!-- 工作内容 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.jobContent')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 报名截止时间 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.deadline')"
        :value="formateToDate(model.deadline)"
        column-flex
      />
    </Col>

    <!-- 其他要求 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.otherRemand')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 岗位单位 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.postUnit')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 实践地址 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.practiceAddress')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 详细地址 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.detailedAddress')"
        :value="model.name"
        column-flex
      />
    </Col>

    <!-- 公司简介 -->
    <Col :span="24">
      <DetailLabelItem
        :label="t('label.companyProfile')"
        :value="model.name"
        column-flex
      />
    </Col>
  </Row>
</template>
