/* eslint-disable import/no-extraneous-dependencies */
function setupDevServer(app, options, cb) {
  const fs = require('fs');
  const MemoryFileSystem = require('memory-fs');
  const webpack = require('webpack');
  const webpackDevMiddleware = require('webpack-dev-middleware');
  const webpackHotMiddleware = require('webpack-hot-middleware');

  const compileModule = require('./compile-module');


  return new Promise((res) => {
    const {
      vueSSRClientManifestPath,
      vueSSRServerBundlePath,
      webpackClientConfig,
      webpackServerConfig,
      hotClientWebSocketPort,
      templatePath,
    } = options;

    let renderer;
    let template;
    let clientManifest;

    function update() {
      if (renderer && template && clientManifest) {
        cb({ renderer, template, clientManifest });
        res({ renderer, template, clientManifest });
      }
    }

    webpackClientConfig.entry.app = ['webpack-hot-middleware/client', webpackClientConfig.entry.app];
    webpackClientConfig.plugins.push(
      new webpack.HotModuleReplacementPlugin(),
      new webpack.NoEmitOnErrorsPlugin(),
    );

    const clientCompiler = webpack(webpackClientConfig);

    const outputFileSystem = new MemoryFileSystem();
    const devMiddleware = webpackDevMiddleware(clientCompiler, {
      outputFileSystem,
      publicPath: webpackClientConfig.output.publicPath,
      index: false,
      stats: {
        colors: true,
      },
    });

    app.use(devMiddleware);

    clientCompiler.hooks.beforeCompile.tap('call', () => {
      // eslint-disable-next-line no-console
      console.log('********** Waiting for webpack building **********');
    });

    clientCompiler.hooks.done.tap('done', (stats) => {
      const statsObj = stats.toJson();
      // eslint-disable-next-line no-console
      // statsObj.errors.forEach((err) => console.error('clientCompiler.err:', err));
      // eslint-disable-next-line no-console
      // statsObj.warnings.forEach((warn) => console.warn('clientCompiler.warn:', warn));
      if (statsObj.errors.length > 0) return;

      const mfs = devMiddleware.context.outputFileSystem;
      template = fs.readFileSync(templatePath, 'utf8');
      clientManifest = JSON.parse(mfs.readFileSync(vueSSRClientManifestPath, 'utf-8'));

      update();
      // eslint-disable-next-line no-console
      console.log('********** Webpack built **********');
    });

    app.use(webpackHotMiddleware(clientCompiler, {
      port: hotClientWebSocketPort,
    }));

    // watch and update server renderer
    const serverCompiler = webpack(webpackServerConfig);
    const mfs = new MemoryFileSystem();
    serverCompiler.outputFileSystem = mfs;
    serverCompiler.watch({}, (err) => {
      if (err) throw err;
      // const statsObj = stats.toJson();
      // statsObj.errors.forEach((err2) => console.error('serverCompiler.err:', err2));
      // statsObj.warnings.forEach((warn) => console.warn('serverCompiler.warn:', warn));

      const serverManifest = JSON.parse(mfs.readFileSync(vueSSRServerBundlePath, 'utf-8'));
      const evaluate = compileModule(serverManifest.files, false, false);
      renderer = evaluate(webpackServerConfig.output.filename, global);
      update();
    });
  });
}

module.exports = setupDevServer;
