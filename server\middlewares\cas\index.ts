/**
 * 说明
 * CASAuthentication
 * createCasTicketMiddlewares
 * createTokenMiddlewares
 */
module.exports = function createCASMiddlewares() {
  const createCasAuthentication = require('./cas-authentication');
  const createCasTicketMiddlewares = require('./cas-ticket');
  const createTokenMiddlewares = require('./token');
  const createLoginStatusMiddleware = require('./login-status');
  const createLogoutCheckerMiddleware = require('./logout-checker');

  return {
    createCasAuthentication,
    createCasTicketMiddlewares,
    createTokenMiddlewares,
    createLoginStatusMiddleware,
    createLogoutCheckerMiddleware,
  };
};
