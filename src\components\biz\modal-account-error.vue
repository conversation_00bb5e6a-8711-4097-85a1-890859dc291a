<script setup lang="ts">
import { Modal } from 'view-ui-plus';
import { useI18n } from 'vue-i18n';

import { accountErrorI18n } from '@/helps/i18n/account-error';


defineProps<{
  accountErrorCode: string;
}>();

const emit = defineEmits<{
  'on-logout': [];
}>();

const { t } = useI18n();
const modelValue = defineModel<boolean>();

function onClickConfirm() {
  emit('on-logout');
}
</script>


<template>
  <Modal
    v-model="modelValue"
    class-name="pima-modal quit-modal"
    footer-hide
    :transfer="false"
    :width="400"
    :closable="false"
    :mask-closable="false"
  >
    <div class="modal-main">
      <div class="modal-title">
        <img
          src="@/assets/img/icon-error.png"
          alt=""
        >
        <span>{{ accountErrorI18n(accountErrorCode) }}</span>
      </div>

      <Button
        type="primary"
        class="btn-quit"
        @click="onClickConfirm()"
      >
        {{ t('common.action.confirm') }}
      </Button>
    </div>
  </Modal>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';


.quit-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .modal-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .modal-title {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 0 20px;
      color: #ff5c5c;
      font-size: 18px;

      img {
        margin-right: 20px;
      }
    }

    .btn-quit {
      width: 120px;
      height: 38px;
      font-size: 14px;
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      border-radius: 2px;

      &:hover {
        background-color: var(--primary-hover-color);
        border-color: var(--primary-hover-color);
      }
    }
  }
}
</style>
