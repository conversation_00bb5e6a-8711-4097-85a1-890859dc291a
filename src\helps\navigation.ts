import _ from 'lodash';
import type { LocationQueryRaw, LocationQueryValueRaw, RouteLocationNormalized, Router } from 'vue-router';
import {
  RouterName as RN,
  FROM_PARAM_NAME_KEY,
  FROM_ROUTE_NAME_KEY,
} from '@/config/router';

/**
 * 在路由跳转前处理，将来源路由名称存入目标路由的meta信息。
 *
 * @param to - 即将导航到的目标路由对象。
 * @param from - 当前导航正离开的路由对象。
 * @param next - 调用以进行下一步导航的函数。
 */
export function beforeEach(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: (vm?: RouteLocationNormalized) => void,
) {
  Object.assign(to.meta, {
    [FROM_ROUTE_NAME_KEY]: from.name,
  });

  // 调用next函数传递处理后的to路由
  next();
}

/**
 * 检查当前是否处于应用的根路由。
 *
 * @param router - Vue Router实例。
 */
export function isCurrentRoot(router: Router) {
  return RN.Root === router.currentRoute.value.name;
}

/**
 * 导航至应用的根路由。
 *
 * @param router - Vue Router实例。
 */
export function backToRoot(router: Router) {
  router.replace({ name: RN.Root });
}

/**
 * 判断当前是否处于禁止访问的页面。
 *
 * @param router - Vue Router实例。
 */
export function isCurrentForbidden(router: Router) {
  return RN.Forbidden === router.currentRoute.value.name;
}

/**
 * 导航至禁止访问的页面。
 *
 * @param router - Vue Router实例。
 */
export function goForbidden(router: Router) {
  router.replace({ name: RN.Forbidden });
}

/**
 * 判断是否可以执行浏览器后退操作。
 *
 * @param router - Vue Router实例。
 */
export function canGoBack(router: Router) {
  const from = _.get(router.currentRoute.value.query, FROM_PARAM_NAME_KEY, null);
  return !_.isNil(from);
}

/**
 * 执行浏览器后退或根据条件进行路由替换。
 *
 * @param router - Vue Router实例。
 */
export function goBack(router: Router) {
  const fromRouteName = _.get(
    router.currentRoute,
    `meta.${FROM_ROUTE_NAME_KEY}`,
  );
  const queryFromRouteName = _.get(
    router.currentRoute,
    `query.${FROM_PARAM_NAME_KEY}`,
    null,
  );
  // 会记录fromRouteName在route的meta里，如果fromRouteName是null/Root，则表示是当前为浏览器打开的第一个URL
  // 如果不是第一个URL，则可以直接操作浏览器后退
  if (![null, RN.Root].includes(fromRouteName)) {
    router.back();
    // 如果是第一个URL，无法正常操作浏览后退，则使用replace方法替换，需指定queryFromRouteName才生效
  } else if (!_.isNil(queryFromRouteName)) {
    router.replace({
      name: queryFromRouteName,
    });
  }
}

/**
 * 向历史记录中添加新路由并导航。
 *
 * @param router - Vue Router实例。
 * @param name - 目标路由的名称。
 * @param query - 路由的查询参数，默认为空对象。
 */
export const push = (router: Router, { name, query = {}, params = {} }: {
  name: string, query?: LocationQueryRaw, params?: LocationQueryRaw,
}) => {
  router.push({
    name,
    query: {
      ...query,
      [FROM_PARAM_NAME_KEY]: router.currentRoute.value.name as LocationQueryValueRaw,
    },
    params,
  });
};

/**
 * 替换当前路由。
 *
 * @param router - Vue Router实例。
 * @param name - 新路由的名称。
 * @param query - 路由的查询参数，默认为空对象。
 */
export const replace = (router: Router, { name, query = {}, params = {} }) => {
  router.replace({
    name,
    query: {
      ...query,
    },
    params,
  });
};
