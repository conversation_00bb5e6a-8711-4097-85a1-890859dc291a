/**
 * 创建一个文件输入框以供用户选择文件，并在文件选择后触发回调。
 *
 * @param options {PickFilesOptions} - 文件选择配置选项。
 * @returns {void}
 */
import { on } from './dom';

interface PickFilesOptions {
  /** 是否允许选择多个文件。 */
  multiple?: boolean;
  /** 允许的文件类型，例如：'image/*', '.pdf'。 */
  accept?: string;
  /**
   * 当文件被选中时的回调函数。
   * @param result {{ files: FileList }} - 回调参数，包含选中的文件列表。
   */
  onPick: (result: { files: FileList }) => void;
}

interface FileListEvent extends Event {
  target: HTMLInputElement & { files: FileList };
}

export function pickFiles(options: PickFilesOptions): void {
  // 创建一个文件输入元素
  const input = document.createElement('input');
  input.type = 'file'; // 设置为文件输入类型
  input.multiple = options.multiple || false; // 设置是否多选
  input.accept = options.accept; // 设置接受的文件类型
  input.style.display = 'none'; // 隐藏文件输入框 避免取消时 输入框还存在
  document.body.appendChild(input);

  // 触发点击打开文件选择对话框
  input.click();

  // 监听文件输入框的change事件
  const off = on(input, 'change', (event: FileListEvent) => {
    // 将选中文件转换为数组
    const { files } = event.target;
    // 调用用户提供的回调函数并传递文件列表
    options.onPick({ files });

    // 选择完成后移除事件监听器
    off();

    document.body.removeChild(input);
  });
}
