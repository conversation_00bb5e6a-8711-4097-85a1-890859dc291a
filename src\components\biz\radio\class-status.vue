<script setup lang="ts">
import { ClassStatus } from '@/consts/class-management';

import { getClassStatusText } from '@/helps/i18n/class-status';

</script>


<template>
  <RadioGroup
    v-bind="$attrs"
    class="pima-radio-default"
  >
    <Radio
      v-for="value of Object.values(ClassStatus)"
      :key="value"
      :label="value"
    >
      {{ getClassStatusText(value) }}
    </Radio>
  </RadioGroup>
</template>
