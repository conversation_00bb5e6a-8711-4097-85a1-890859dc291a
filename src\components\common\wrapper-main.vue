<script setup lang="ts">

defineOptions({
  name: 'PimaMainWrapper',
});

</script>

<template>
  <Row class="pima-main-wrapper">
    <Col
      span="4"
      class="col-left"
    >
      <slot name="left" />
    </Col>
    <Col
      class="col-right"
    >
      <slot name="right" />
    </Col>
  </Row>
</template>


<style lang="less" scoped>
.pima-main-wrapper {
  height: 100%;
  min-height:auto;

  .col-left {
    min-width: 260px;
  }

  .col-right {
    flex: 1;
    width: 0;
    height: 100%;
    overflow: auto;
  }
}
</style>
