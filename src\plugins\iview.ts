import type { App } from 'vue';
import type { I18n } from 'vue-i18n';
import ViewUIPlus, { Message } from 'view-ui-plus';


/**
 *  iView 插件
 */
export const IViewPlugin = {
  install(app: App, options: {
    i18n?: I18n;
  } = {}) {
    if (!options.i18n) {
      throw new Error('IViewPlugin need i18n option');
    }

    app.use(ViewUIPlus, {
      i18n: options.i18n,
    });

    Message.config({
      top: 14,
    });
  },
};
