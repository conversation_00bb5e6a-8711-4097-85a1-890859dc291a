import { YoN } from '@/consts/y-o-n';
import { namespaceT } from '@/helps/namespace-t';

type MapKeyType = keyof YoN | boolean | number | string;
export function getYoNI18nText(value) {
  const t = namespaceT('consts.common');
  const mapper = new Map<MapKeyType, string>([
    [true, t(YoN.Y)],
    [1, t(YoN.Y)],
    [YoN.Y, t(YoN.Y)],
    [false, t(YoN.N)],
    [0, t(YoN.N)],
    [YoN.N, t(YoN.N)],
  ]);

  return mapper.get(value) || value;
}

export function getStatusI18nText(value) {
  const t = namespaceT('consts.status');
  const mapper = new Map<MapKeyType, string>([
    [true, t(YoN.Y)],
    [1, t(YoN.Y)],
    [YoN.Y, t(YoN.Y)],
    [false, t(YoN.N)],
    [0, t(YoN.N)],
    [YoN.N, t(YoN.N)],
  ]);

  return mapper.get(value) || value;
}
