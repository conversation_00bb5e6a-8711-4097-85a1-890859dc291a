export interface WarningModelType {
  visible: boolean;
  title: string;
  content: string;
  loading: boolean;
  /** 保存中 */
  saving: boolean;
  id?: number;
  [key: string]: unknown;
}


export const createWarningModel = ({ title = '', content = '' }): WarningModelType => {
  return {
    visible: false,
    title: title || '',
    content: content || '',
    loading: false,
    saving: false,
    id: undefined,
  };
};
