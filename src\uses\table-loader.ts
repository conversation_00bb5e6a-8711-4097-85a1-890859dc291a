import { CommonListApi } from '@/api/common/common-list-api';
import { openToastError } from '@/helps/toast';

import type { PaginationParamsOption } from '@/helps/api';


type ListApi<R> = new () => CommonListApi<R>;
type ParamsHandler<T> = (params: T & PaginationParamsOption) => T & PaginationParamsOption;

/**
 * 表格数据加载器Hook
 *
 * 用于创建一个表格数据加载器，封装了数据请求、参数处理和错误处理逻辑
 *
 * @template T - 请求参数类型
 * @template R - 响应数据类型
 *
 * @param {ListApi<R>} ListApi - API类构造函数，必须继承自CommonListApi
 * @param {ParamsHandler<T>} [paramsHandler] - 可选的参数处理器函数，用于在发送请求前处理参数
 *
 * @returns {{
*   loadData: (params: T & PaginationParamsOption) => Promise<R>
* }} 返回包含loadData方法的对象，该方法用于加载表格数据
*/
export function useTableLoader<T, R>(
  ListApi: ListApi<R>,
  paramsHandler?: ParamsHandler<T>,
) {
  const loadData = async (params: T & PaginationParamsOption) => {
    try {
      const api = new ListApi();

      api.params = paramsHandler ? paramsHandler(params) : params;

      const res = await api.send();
      return res;
    } catch (error) {
      openToastError(error.message);
      throw error;
    }
  };

  return loadData;
}
