<template>
  <RadioGroup
    class="pima-radio-group-vertical"
    v-bind="$attrs"
    v-on="$attrs"
  >
    <Radio
      v-for="item in options"
      :key="`${item.value}`"
      :label="item.value"
    >
      <span>{{ item.label }}</span>
    </Radio>
  </RadioGroup>
</template>


<script lang='ts'>
import { defineComponent } from 'vue';
import type { PropType } from 'vue';


export default defineComponent({
  name: 'RadioGroupOptions',

  props: {
    options: {
      type: Array as PropType<Array<{ label: string, value: string }>>,
      default: undefined,
    },
  },

  emits: [
  ],

  setup() {
    return {
    };
  },
});
</script>
