<script setup lang="ts">

interface Props {
  step?:number,
  precision?:number,
  disabled?:boolean,
  min?:number
  max?:number,
}

withDefaults(defineProps<Props>(), {
  step: 1,
  precision: 0,
  disabled: false,
  min: 0,
  max: 9999,
});


</script>


<template>
  <InputNumber
    class="pima-input-number"
    v-bind="$attrs"
    :step="step"
    :precision="precision"
    :disabled="disabled"
    :min="min"
    :max="max"
  />
</template>
