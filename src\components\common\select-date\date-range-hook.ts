import _ from 'lodash';
import { unref } from 'vue';
import { isAfter, isBefore, endOfDay, startOfDay, addDays } from 'date-fns';

// 日期选择限制：不能小于开始日期 || 大於结束日期
export function dateRangeHook(startDate, endDate, interval: number) {
  const state = {
    minDateDisabled: (date) => {
      const current = startOfDay(new Date(date));
      if (_.isNil(unref(endDate))) {
        return false;
      }

      const endDatePlusInterval = addDays(new Date(unref(endDate)), -1 * interval);

      return isAfter(current, endDatePlusInterval);
    },

    maxDateDisabled: (date) => {
      const current = endOfDay(new Date(date));
      if (_.isNil(unref(startDate))) {
        return false;
      }

      const startDatePlusInterval = addDays(new Date(unref(startDate)), interval);

      return isBefore(current, startDatePlusInterval);
    },
  };

  return state;
}
