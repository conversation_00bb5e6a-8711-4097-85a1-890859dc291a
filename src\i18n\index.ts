import { createI18n } from 'vue-i18n';
import { Locale, FALLBACK_LOCALE } from '@/config/locale';
import zhCN from './zh-CN';
import enUS from './en-US';


export const i18n = createI18n<[
  typeof zhCN,
  typeof enUS,
], Locale>({
  locale: Locale.ZH_CN,
  legacy: false,
  fallbackLocale: FALLBACK_LOCALE,
  messages: {
    [Locale.ZH_CN]: zhCN,
    [Locale.EN_US]: enUS,
  },
  silentTranslationWarn: (process.env.NODE_ENV === 'production'),
});
