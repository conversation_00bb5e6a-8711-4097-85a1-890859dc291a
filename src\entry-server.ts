import URI from 'urijs';

import { BaseError } from '@/errors/base-error';
import { createApp } from './main';


function getStaticResourcesBaseUri() {
  return new URI(process.env.STATIC_RESOURCES_BASE_URL);
}

// Favicon文件地址
function getFaviconUrl() {
  const uri = getStaticResourcesBaseUri();
  uri.segment('favicon.ico');

  return uri.toString();
}

// 主题样式文件地址
function getThemeIViewCssUrl() {
  const uri = getStaticResourcesBaseUri();
  uri.segment('css');
  uri.segment('cultivation-theme-iview');
  uri.segment(process.env.THEME_IVIEW_VERSION || 'latest');
  uri.segment('default.min.css');

  if (process.env.THEME_IVIEW_FLAG) {
    uri.search(process.env.THEME_IVIEW_FLAG);
  }

  return uri.toString();
}


// 富文本编辑器样式文件地址
function getEditorCssUrl() {
  const uri = getStaticResourcesBaseUri();

  uri.segment('css');
  uri.segment('pima-editor');
  uri.segment(process.env.THEME_IVIEW_VERSION || 'latest');
  uri.segment('default.min.css');

  return uri.toString();
}

function getLang(locale: string) {
  return locale.includes('en') ? 'en' : 'zh';
}

/**
 * 应用程序启动上下文接口，封装了初始化时所需的关键参数。
 */
interface Context {
  /**
   * 应用程序的区域设置代码，用于国际化配置。
   */
  appLocale: string;

  /**
   * 表示用户当前的登录状态。
   */
  appLoggedIn: boolean;

  /**
   * 应用启动时尝试导航的初始URL。
   */
  url: string;
}


/**
 * 默认导出的异步初始化函数，接收一个上下文对象并配置应用。
 *
 * @param {Object} ctx - 应用上下文，包含appLocale、appLoggedIn及url等信息。
 *
 * 函数内部：
 * 1. 初始化应用实例，传入地区设置和登录状态。
 * 2. 根据上下文中的url跳转路由。
 * 3. 等待路由准备完毕，检查路由是否匹配有效组件。
 * 4. 若路由未匹配到任何组件，则抛出'ROUTE_NOT_FOUND'错误。
 * 5. 实施认证逻辑：检查路由是否需要认证（通过meta.requiresAuth），
 *    用户登录状态（ctx.appLoggedIn），以及特殊认证票据（ticket）。
 * 6. 若访问受限路由且未登录，则抛出'LOGIN_REQUIRED'错误。
 * 7. 准备客户端渲染所需的数据：语言、标题、Pinia状态、JavaScript提示、
 *    以及动态生成的HTML元数据头部信息（favicon、主题样式）。
 * 8. 所有检查通过后，使用resolve返回配置好的应用实例；若过程中出错，则通过reject传递错误。
 */
export default (ctx: Context) => {
  return new Promise((resolve, reject) => {
    const { app, router, pinia, i18n } = createApp({
      locale: ctx.appLocale,
      loggedIn: ctx.appLoggedIn,
    });

    router.push(ctx.url);
    router.isReady().then(() => {
      const { matched } = router.currentRoute.value;
      if (matched.length === 0) {
        const err = new BaseError("Can't find the corresponding route");
        err.code = 'ROUTE_NOT_FOUND';
        reject(err);
        return;
      }

      // 如果在路由配置里 meta.requiresAuth 为 false，则为当前路由可不通过认证访问，反之亦然
      // meta.requiresAuth 不配置或配置为 true，则为需要认证
      // 当前用户的认证状态保存于 ctx.appLoggedIn，该值由server端维护
      // 当当前访问地址带有 ticket 时，需要进行放行，该参数为 CAS 认证成功后返回值
      if (!router.currentRoute.value.query?.ticket
        && router.currentRoute.value.meta?.requiresAuth !== false
        && ctx.appLoggedIn === false) {
        const err = new BaseError('Login required');
        err.code = 'LOGIN_REQUIRED';
        reject(err);
        return;
      }

      // 如果URL中带有 login 参数，且当前用户未登录，则需要进行登录
      if ('login' in router.currentRoute.value.query
        && ctx.appLoggedIn === false) {
        const err = new BaseError('Login required');
        err.code = 'LOGIN_REQUIRED';
        reject(err);
        return;
      }


      Object.assign(ctx, {
        // @ts-expect-error:  Property 'value' does not exist on type 'string'
        lang: getLang(i18n.global.locale.value),
        title: i18n.global.t('title'),
        // state 生成脚本 window.__INITIAL_STATE__
        state: pinia.state.value,
        enableJavaScriptTips: i18n.global.t('enableJavaScriptTips'),
        meta: `<link rel="icon" type="image/x-icon" href="${getFaviconUrl()}">
    <link rel="stylesheet" href="${getThemeIViewCssUrl()}">
    <link rel="stylesheet" href="${getEditorCssUrl()}">`,
      });

      resolve(app);
    }).catch(reject);
  });
};
