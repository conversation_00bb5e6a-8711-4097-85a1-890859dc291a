<script lang="ts" setup>
import { pickFiles } from '@/utils/pick-files';

import { openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';

const props = withDefaults(defineProps<{
  loading: boolean,
  accept: string,
  access: Array<any>,
  tips?: string,
  copys?: number,
  length?: number,
  limitSize?: number,
}>(), {
  loading: false,
  accept: '',
  access: () => [],
  tips: '',
  copys: 0,
  length: 0,
  limitSize: 10,
});

const emit = defineEmits([
  'on-upload',
]);


const t = namespaceT('common');

function validType(file) {
  if (props.access.length === 0) {
    return true;
  }
  const suffix = file.name.substring(file.name.lastIndexOf('.'));

  const valid = props.access.some((o) => o.trim().toLowerCase() === suffix.trim().toLowerCase());
  if (!valid) {
    openToastError(t('error.fileTypeError', { accept: props.access.join('、') }));
  }

  return valid;
}

function validSize(file) {
  if (props.limitSize === 0) {
    return true;
  }

  const limitSize = props.limitSize * 1024 * 1024;
  if (file.size > limitSize) {
    openToastError(t('error.fileSizeError', { size: props.limitSize }));
    return false;
  }
  return true;
}

function validCopys() {
  if (props.copys === 0) {
    return true;
  }

  if (props.length >= props.copys) {
    openToastError(t('error.fileCopiesError', { num: props.copys }));
    return false;
  }

  return true;
}

function onPick({ files }) {
  const file = files[0];
  if (validType(file) && validSize(file) && validCopys()) {
    emit('on-upload', file);
  }
}

function onUpload() {
  pickFiles({
    accept: props.accept,
    onPick,
  });
}
</script>


<template>
  <div class="block-not-uploaded">
    <Button
      class="pima-btn"
      icon="ios-cloud-upload-outline"
      :loading="loading"
      @click="onUpload()"
    >
      {{ t('action.upload') }}
    </Button>

    <div
      v-if="tips"
      class="recommend-size"
    >
      {{ tips }}
    </div>
  </div>
</template>


<style lang="less" scoped>
.block-not-uploaded {
  display: inline-flex;
  align-items: center;

  .recommend-size {
    margin-left: 10px;
  }
}
</style>
