import { AttachmentVO } from '^/types/attachment';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';
import { CommonUploadApi } from '../common/common-upload-api';

enum ErrorCode {
  ATTACHMENT_DISALLOWED_EXTENSIONS_ERROR = 'ATTACHMENT_DISALLOWED_EXTENSIONS_ERROR',
  ATTACHMENT_NAME_LENGTH = 'ATTACHMENT_NAME_LENGTH',
}

export class UploadApi extends CommonUploadApi<{ model:AttachmentVO }> {
  url(): string {
    return '/attachments/upload';
  }

  async send():Promise<{ model:AttachmentVO }> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.dfs');
      switch (error.code) {
        case ErrorCode.ATTACHMENT_DISALLOWED_EXTENSIONS_ERROR:
        case ErrorCode.ATTACHMENT_NAME_LENGTH:
          throw new BaseError(t(error.code));

        default:
          throw error;
      }
    }
  }
}
