<script setup lang="ts">
import { ref, onBeforeMount, reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import NavFormWrap from '@/components/common/nav-form-wrap.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import DetailForm from './components/detail-form.vue';
import ActionLog from '@/components/biz/action-log.vue';

import type { ActionLogType } from '^/types/action-log';

import { DetailApi } from '@/api/job-post/detail';
import { goBack } from '@/helps/navigation';
import { openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';

import { createFormModel } from '../../helps/models';
import { handleDetailData } from '../../helps/handle-api-data';


const route = useRoute();
const router = useRouter();
const t = namespaceT('jobPost');

const loading = ref(false);
const model = reactive(createFormModel());
const actionLog = ref<ActionLogType[]>([]);

const id = computed(() => route.params.id);

const fetchDetail = async () => {
  try {
    loading.value = true;
    const api = new DetailApi({ id: id.value });
    const res = await api.sendWithSpecifyType();
    Object.assign(model, handleDetailData(res));
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};


const getActionLog = async () => {
  // const api = new ApplyProcessQueryActionLogApi({ id:id.value });
  // const res = await api.send();
  // actionLog.value = res.data;
};

onBeforeMount(() => {
  fetchDetail();

  getActionLog();
});
</script>


<template>
  <div class="pima-form-page">
    <TitleBar
      go-back
      :title="t('title.detail')"
      @go-back="goBack(router)"
    />
    <WrapperForm :loading="loading">
      <NavFormWrap :nav-bar="null">
        <WrapperFormTitle :title="t('title.detail')">
          <DetailForm
            v-model="model"
          />
        </WrapperFormTitle>

        <template #right>
          <ActionLog
            :data-source="actionLog"
            :title="t('actionLog.title')"
            :empty-text="t('actionLog.emptyText',{title:t('actionLog.title')})"
          />
        </template>
      </NavFormWrap>
    </WrapperForm>
  </div>
</template>
