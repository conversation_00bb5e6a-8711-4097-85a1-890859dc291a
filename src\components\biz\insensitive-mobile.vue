<script setup lang="ts">
import { useDesensitizeMobile } from '@/uses/use-desensitize-mobile';


interface Props {
  mobile: string;
}

const props = defineProps<Props>();

const mobileInfo = useDesensitizeMobile(props.mobile);


</script>


<template>
  <div
    class="insensitive-mobile"
    @click="mobileInfo.onToggleStatus"
  >
    {{ mobileInfo.mobile }}
  </div>
</template>


<style lang="less" scoped>
.insensitive-mobile {
    cursor: pointer;

    &:hover{
      color: var(--primary-color);
    }
}
</style>
