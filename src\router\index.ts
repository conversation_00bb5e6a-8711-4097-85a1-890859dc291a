import { createRouter as _createRouter, createWebHistory, createMemoryHistory } from 'vue-router';

import { useLoginStatusStore } from '@/store/login-status';
import { beforeEach } from '@/helps/navigation';
import { isServer } from '@/utils/is';
import { routes } from './routes';


export function createRouter() {
  const router = _createRouter({
    history: isServer() ? createMemoryHistory() : createWebHistory(),
    routes,
  });

  const store = useLoginStatusStore();

  router.beforeEach((to, from, next) => {
    if (!isServer()) {
      // 如果在路由配置里 meta.requiresAuth 为 false，则为当前路由可不通过认证访问，反之变然
      // meta.requiresAuth 不配置或配置为 true，则为需要认证
      // 当前用户的认证状态保存于 useLoginStatusStore
      // 本逻辑只处理客户端，不处理服务器端
      // 当由不需要认证的界面，进入需要认证的界面，则进行跳转，跳转后由服务器端进行身份认证
      if (to.meta.requiresAuth !== false
        && store.loggedIn === false) {
        next(false);
        const { href } = router.resolve(to);
        window.location.href = href;
        return;
      }
    }

    beforeEach(to, from, next);
  });

  return router;
}
