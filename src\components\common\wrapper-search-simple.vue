<script lang='ts' setup>
import { namespaceT } from '@/helps/namespace-t';

withDefaults(defineProps<{
  show?: boolean;
  advanced?: boolean;
  title?: string;
}>(), {
  show: true,
  advanced: false,
  title: '',
});

interface EmitType {
  (e: 'show-search-advanced'): void
}
const emit = defineEmits<EmitType>();

const t = namespaceT('common');

function onShow() {
  emit('show-search-advanced');
}
</script>


<template>
  <div class="pima-search-simple-wrapper">
    <div
      v-show="show"
      class="left"
    >
      <div
        v-if="title"
        class="menu-title"
        v-text="title"
      />

      <slot />

      <!-- 高级搜索按钮 -->
      <template v-if="advanced">
        <Button
          type="text"
          class="ml-15 pima-btn"
          @click="onShow"
        >
          {{ t('action.advancedSearch') }}
        </Button>
      </template>
    </div>

    <div
      v-show="!show"
      class="left"
    >
      <div
        v-if="title"
        class="menu-title"
        v-text="title"
      />
    </div>

    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>
