<script setup lang="ts">
import { onBeforeMount } from 'vue';


import { type CascaderRegionsItem } from '@/api/client/regions';
import { openToastError } from '@/helps/toast';
import { useRegionsStore } from '@/store/regions';


const regionsStore = useRegionsStore();


const loadData = async (item:CascaderRegionsItem, callback: () => void) => {
  try {
    Object.assign(item, { loading: true });
    const list = await regionsStore.fetch(item.value);
    Object.assign(item, { children: list });
    /** 调用callback 立即展开子级菜单 */
    callback();
  } catch (error) {
    openToastError(error.message);
  } finally {
    Object.assign(item, { loading: false });
  }
};


onBeforeMount(() => {
  regionsStore.fetch();
});

</script>


<template>
  <Cascader
    v-bind="$attrs"
    class="pima-cascader"
    :data="regionsStore.regions"
    :load-data="loadData"
  />
</template>
