<script lang='ts' setup>
import { defineAsyncComponent } from 'vue';

import ClientOnly from '@/components/common/client-only';
import { namespaceT } from '@/helps/namespace-t';

// eslint-disable-next-line import/no-unresolved
const PimaForbidden = defineAsyncComponent(() => import('pimaRemoteUI/PimaForbidden'));


const t = namespaceT('common');
function onGoBack() {
  window.history.back();
}
</script>


<template>
  <ClientOnly class="full-height">
    <PimaForbidden
      :tips="t('error.noAuth')"
      @go-back="onGoBack()"
    />
  </ClientOnly>
</template>
