import { createApp as _createApp } from 'vue';
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia';

import { setupInjectPrototypePlugin } from '@/plugins/inject-prototype';
import { AuthPlugin } from '@/plugins/auth';
import { PimaRemoteUIPlugin } from '@/plugins/pima-remote-ui';
import { IViewPlugin } from '@/plugins/iview';
import { SUPPORT_LOCALES, FALLBACK_LOCALE } from '@/config/locale';
import { useI18nStore } from '@/store/i18n';
import { useLoginStatusStore } from '@/store/login-status';
import { i18n } from '@/i18n';
import { createRouter } from '@/router';
import App from '@/app.vue';

import 'view-ui-plus/dist/styles/viewuiplus.css';
import '@/styles/app.less';
import '@/styles/utils.less';
import '@/styles/theme.less';


/**
 * 定义创建应用程序配置所需的数据结构。
 */
type CreateAppConfig = {
  /**
   * 区域设置或语言环境代码，用于确定用户界面的语言和区域习惯。
   */
  locale: string,

  /**
   * 指示用户当前是否已经通过认证并处于登录状态。
   * @defaultValue false
   */
  loggedIn: boolean,
};

/**
 * 创建应用程序实例。
 *
 * 该函数负责初始化和配置应用程序的各种核心组件，如状态管理（Pinia）、国际化（i18n）、路由（router）等。
 * 它根据传入的配置对象来调整应用程序的初始化状态，以满足不同环境（服务器端或客户端）和用户登录状态的需求。
 *
 * @param {CreateAppConfig} config 应用程序创建的配置对象，包含locale（语言环境）、isServer（是否为服务器端）、loggedIn（用户是否已登录）。
 * @returns 返回一个对象，包含应用程序实例、路由实例、状态管理实例和国际化实例。
 */
export function createApp({ locale, loggedIn }: CreateAppConfig) {
  // 创建应用程序实例。
  const app = _createApp(App);
  // 初始化状态管理器Pinia。
  const pinia = createPinia();

  // 使用Pinia创建登录状态存储。
  const loginStatusStore = useLoginStatusStore(pinia);
  // 设置登录状态。
  loginStatusStore.loggedIn = loggedIn; // 该值的设置必须早于创建 router

  // 根据isServer配置创建路由实例。
  const router = createRouter();

  // 使用Pinia创建国际化状态存储。
  const i18nStore = useI18nStore(pinia);
  // 设置全局语言。如果指定的语言不在支持的语言列表中，则默认使用Fallback语言。
  // @ts-expect-error:  Property 'value' does not exist on type 'string'
  i18n.global.locale.value = SUPPORT_LOCALES.includes(locale) ? locale : FALLBACK_LOCALE;
  // 同步i18n的locale到存储中，以供后续使用。
  // @ts-expect-error:  Property 'value' does not exist on type 'string'
  i18nStore.locale = i18n.global.locale.value; // server端初始化使用，后续不再使用，直接用i18n

  // 应用各种插件和中间件来扩展应用程序功能。
  app.use(pinia);
  app.use(PiniaVuePlugin);
  app.use(i18n);
  app.use(setupInjectPrototypePlugin); // 全局方法
  app.use(AuthPlugin);
  app.use(IViewPlugin, { i18n });
  app.use(PimaRemoteUIPlugin, { i18n });
  app.use(router);

  // 返回配置好的应用程序实例和其他重要组件的实例。
  return {
    app,
    router,
    pinia,
    i18n,
  };
}
