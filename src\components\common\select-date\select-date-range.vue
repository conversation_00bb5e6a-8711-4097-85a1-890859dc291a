<script lang="ts" setup>
import { reactive, toRefs, useAttrs, watch } from 'vue';
import { getHours, getMinutes } from 'date-fns';
import _ from 'lodash';
import { useI18n } from 'vue-i18n';

import { isBeforeDay } from '@/helps/date';

import { dateRangeHook } from './date-range-hook';

import SelectSingleDate from './select-single-date.vue';

interface Props {
  datetime?: boolean;
  type?: string;
  startDate: string | Date | null;
  endDate: string | Date | null;
  disabledIntervalDays?: number; // 禁用日期的建隔天数
  startPlaceholder?: string;
  endPlaceholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  datetime: false,
  type: 'date',
  startDate: null,
  endDate: null,
  disabledIntervalDays: 0,
  startPlaceholder: null,
  endPlaceholder: null,
});

interface EmitType {
  (e: 'update:startDate', startDate: string | Date | null): void
  (e: 'update:endDate', endDate: string | Date | null): void
}
const emit = defineEmits<EmitType>();

const { t } = useI18n();
const attrs = useAttrs();
const { startDate, endDate } = toRefs(props);
const { minDateDisabled, maxDateDisabled } = dateRangeHook(startDate, endDate, props.disabledIntervalDays);

function createTimePickerOptions() {
  return {
    disabledHours: [],
    disabledMinutes: [],
  };
}

const minTimePickerOptions = reactive(createTimePickerOptions());
const maxTimePickerOptions = reactive(createTimePickerOptions());

function onStartDateChange(val) {
  emit('update:startDate', val);
}

function onEndDateChange(val) {
  emit('update:endDate', val);
}

function resetTimePickerOptions() {
  Object.assign(minTimePickerOptions, createTimePickerOptions());
  Object.assign(maxTimePickerOptions, createTimePickerOptions());
}

function changeTimePickerOptions(min, max) {
  resetTimePickerOptions();
  if (min && max) {
    if (!(isBeforeDay(min, max))) {
      const minHours = getHours(min);
      const maxHours = getHours(max);
      minTimePickerOptions.disabledHours = _.range(maxHours + 1, 24);
      maxTimePickerOptions.disabledHours = _.range(minHours);

      const minMinutes = getMinutes(min);
      const maxMinutes = getMinutes(max);
      if (minHours === maxHours) {
        minTimePickerOptions.disabledMinutes = _.range(maxMinutes, 60);
        maxTimePickerOptions.disabledMinutes = _.range(minMinutes + 1);
      }
    }
  }
}

function openWatchDateTimeIfNeed() {
  if (props.type === 'datetime') {
    watch([startDate, endDate], ([min, max]) => {
      changeTimePickerOptions(min, max);
      // correctDate(min, max);
    });
  }
}

openWatchDateTimeIfNeed();

</script>

<template>
  <div class="day-range-picker">
    <SelectSingleDate
      class="pima-date-picker date-picker"
      :class="{ 'pima-date-picker--time': type === 'datetime' }"
      v-bind="attrs"
      :type="type"
      :model-value="startDate"
      :disabled-date="minDateDisabled"
      :time-picker-options="minTimePickerOptions"
      :placeholder="startPlaceholder|| t('common.placeholder.startTime')"
      @input="onStartDateChange"
    />
    <slot name="separator">
      <span class="separator">&ndash;</span>
    </slot>
    <SelectSingleDate
      class="pima-date-picker date-picker"
      :class="{ 'pima-date-picker--time': type === 'datetime' }"
      :type="type"
      :model-value="endDate"
      :disabled-date="maxDateDisabled"
      :time-picker-options="maxTimePickerOptions"
      :placeholder="endPlaceholder || t('common.placeholder.endTime')"
      v-bind="attrs"
      @input="onEndDateChange"
    />
  </div>
</template>


<style lang="less" scoped>
.day-range-picker {
  display: flex;
  align-items: center;
  min-width: 180px;

  .date-picker {
    flex: 1;
    width: 140px;
  }

  .pima-date-picker--time {
    width: 180px;
  }

  .separator {
    flex-shrink: 0;
    margin: 0 10px;
  }
}
</style>
